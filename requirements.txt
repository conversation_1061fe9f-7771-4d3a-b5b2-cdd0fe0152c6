# Core Python dependencies for Robotour Vehicle Project
# Python 3.8+ required

# System monitoring and process management
psutil>=5.8.0

# Optional dependencies for real hardware integration
# Uncomment and install as needed for actual hardware

# GPS module dependencies
# pyserial>=3.5          # For serial GPS communication
# pynmea2>=1.18.0        # For NMEA sentence parsing
# gpsd-py3>=0.3.0        # For GPSD daemon integration

# Vision/Camera dependencies  
# opencv-python>=4.5.0   # For camera input and computer vision
# numpy>=1.21.0          # For image processing
# pillow>=8.3.0          # For image manipulation

# OpenStreetMap and routing dependencies
# osmnx>=1.1.0           # For OpenStreetMap data and routing
# networkx>=2.6.0        # For graph-based routing algorithms
# folium>=0.12.0         # For map visualization
# requests>=2.26.0       # For API calls to map services

# Motor control dependencies (hardware specific)
# RPi.GPIO>=0.7.0        # For Raspberry Pi GPIO control
# adafruit-circuitpython-motor>=3.4.0  # For motor driver control
# pigpio>=1.78           # For precise PWM control

# Communication dependencies
# websockets>=10.0       # For WebSocket communication
# flask>=2.0.0           # For REST API server
# socketio>=5.3.0        # For real-time communication

# Data processing and logging
# pandas>=1.3.0          # For data analysis and logging
# matplotlib>=3.4.0      # For data visualization
# scipy>=1.7.0           # For scientific computing

# Configuration and utilities
# pyyaml>=5.4.0          # For YAML configuration files
# click>=8.0.0           # For command-line interface
# colorama>=0.4.0        # For colored terminal output

# Development and testing dependencies
# pytest>=6.2.0         # For unit testing
# pytest-cov>=2.12.0    # For test coverage
# black>=21.7.0          # For code formatting
# flake8>=3.9.0          # For code linting
# mypy>=0.910            # For type checking

# Note: This is a minimal requirements file for the basic structure.
# Additional dependencies will be needed based on specific hardware
# and implementation requirements.
