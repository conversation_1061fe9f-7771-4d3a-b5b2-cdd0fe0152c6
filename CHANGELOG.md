# Changelog - Kvalifikacny Program

## Verzia 1.3 - 2025-01-29

### Zlepsenie detekcie prekazky a zrusenie obchadzania

**Problemy v predoslych verziach:**
- Robot sa zastavoval prilis casto kvoli citlivej detekcii prekazky
- Objekty sa priblizovali ku kamere a spustali falošne alarmy
- Obchadzaci manever bol komplikovany a nepotrebny

**Riesenie:**
1. **Nova detekcia velkej prekazky** - deteku<PERSON> len prekazky, ktore zaberaju viac ako X% obrazovej plochy
2. **Konfigurovatelny prah** - moznost nastavit prah detekcie (default 20%)
3. **Zrusenie obchadzania** - robot len caka 10s a potom pokracuje dopredu
4. **Lepsia vizualizacia** - zobrazenie prahu detekcie v grafickom rozhrani

### Technicke detaily

#### Nova detekcia prekazky
```python
def detect_large_obstacle(self, frame):
    # Konverzia na grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Detekcia hran pomocou Canny
    edges = cv2.Canny(blurred, 50, 150)

    # Vypocet percenta obrazu s hranami
    edge_percentage = (edge_pixels / total_pixels) * 100

    # Kontrola ci prekazka zabera viac ako threshold %
    return edge_percentage > self.obstacle_threshold_percent
```

#### Konfigurovatelny prah
- Moznost nastavit pri spusteni programu
- Default hodnota: 20%
- Rozsah: 5% - 50% (odporucane)

#### Zjednodusena logika
- Pri detekcii prekazky: zastav na 10s
- Po 10s: pokracuj v jazde bez ohladu na prekazku
- Ziadne obchadzanie ani komplikovane manvre

## Verzia 1.2 - 2025-01-29

### Oprava problemov z verzie 1.1

**Problemy v v1.1:**
- Detekcia prekazky nefungovala spravne (robot nejde dopredu)
- Divna kombinacia stavov (forward + prekazka + motor stoji)
- Komplikovana detekcia referencneho obrazu sposobovala problemy

**Riesenie:**
- Navrat k povodnej funkcnej detekcii prekazky z camera modulu
- Zachovanie len potrebnych zmien (rychlost otacania + zastavenie koles)
- Odstranenie problematickej detekcie referencneho obrazu

## Verzia 1.1 - 2025-01-29 (PROBLEMATICKA - OPRAVENA)

### Zlepsenia na zaklade testov

#### 1. Znizenie rychlosti pri otacacom manevri
**Problem**: Vysoke rychlosti (30%) pri otacacom manevri sposobovali problemy
**Riesenie**: Zmenena rychlost otacania z 30% na 10% (rovnaka ako jazda)

```python
# Predtym:
self.robot.turn_right(30)  # 30% rychlost

# Teraz:
self.robot.turn_right(self.speed)  # 10% rychlost
```

**Vyhody**:
- Plynulejsie otacanie
- Mensie namahanie motorov
- Presnejsie manvre
- Konzistentna rychlost pocas celeho programu
- ✅ **ZACHOVANE v v1.2**

#### 2. Zabezpecenie zastavenia koles na konci
**Problem**: Kolesa sa stale tocili po ukonceni programu
**Riesenie**: Dvojite zastavenie s kratkou pauzou

```python
# Zastavenie na konci - zabezpecime ze sa kolesa zastavia
self.robot.stop_motors()
time.sleep(0.5)  # Kratke cakanie
self.robot.stop_motors()  # Druhe zastavenie pre istotu
```

**Vyhody**:
- Garantovane zastavenie motorov
- Bezpecnost po ukonceni programu
- Jasna signalizacia ukoncenia
- ✅ **ZACHOVANE v v1.2**

#### 3. Inteligentna detekcia prekazky s referencnym obrazom (ODSTRANENE)
**Problem**: Detekcia prekazky nebola dostatocne presna
**Pokus o riesenie**: Implementacia referencneho obrazu po odpocitavani
**Vysledok**: ❌ **NEFUNGOVALO - robot sa nehyboval, detekcia bola prilis citliva**

**Novy algoritmus**:
1. Po nacitani QR kodu zacne odpocitavanie
2. Po 2 sekundach odpocitavania (pri poslednej sekunde) sa nastavi referencny obraz
3. Uzivatel ma cas zmiznut z kamery
4. Detekcia prekazky porovnava aktualny obraz s referencnym

```python
def set_reference_frame(self):
    """Nastavenie referencneho obrazu pre detekciu prekazok"""
    frame = self.camera.capture_frame()
    if frame is not None:
        self.reference_frame = frame.copy()
        self.reference_set = True
```

**Detekcia nahlej prekazky**:
- ROI (Region of Interest) v strede obrazu
- Porovnanie s referencnym obrazom
- Prah 15% zmeny pre detekciu prekazky
- Zameranie na vzdialenost 0-100cm

#### 3. Zabezpecenie zastavenia koles na konci
**Problem**: Kolesa sa stale tocili po ukonceni programu
**Riesenie**: Dvojite zastavenie s kratkou pauzou

```python
# Zastavenie na konci - zabezpecime ze sa kolesa zastavia
self.robot.stop_motors()
time.sleep(0.5)  # Kratke cakanie
self.robot.stop_motors()  # Druhe zastavenie pre istotu
```

**Vyhody**:
- Garantovane zastavenie motorov
- Bezpecnost po ukonceni programu
- Jasna signalizacia ukoncenia

### Technicke detaily

#### Detekcia prekazky - ROI
```python
# ROI v strede obrazu (stredna tretina horizontalne, spodna polovica vertikalne)
roi_x_start = width // 3
roi_x_end = 2 * width // 3
roi_y_start = height // 2
roi_y_end = height
```

#### Prah detekcie
```python
# Prah pre detekciu zmeny
threshold = 30  # Grayscale rozdiel
obstacle_threshold = 0.15  # 15% zmena pixelov
```

#### Graficke vylepšenia
- Vyznacenie ROI oblasti na obraze
- Indikator stavu referencneho obrazu
- Presnejsie informacie o detekcii prekazky

### Testovanie

#### Odporucany postup testovania
1. Spustit program
2. Ukazat QR kod
3. Pocas odpocitavania zmiznut z kamery (po 2 sekundach)
4. Sledovat detekciu prekazky v grafickom rozhrani
5. Testovat obchadzaci manever

#### Diagnostika
- ROI oblast je vyznacena zltym ramcekom
- Stav referencneho obrazu je zobrazeny v informaciach
- Detekcia prekazky je farebne oznacena

### Kompatibilita

Vsetky zmeny su spetne kompatibilne:
- Zachovane API motordriver a camera kniznic
- Zachovane konfiguracne parametre
- Zachovane logovanie a diagnostika

### Buduci vyvoj

Mozne dalsie vylepsenia:
- Kalibrácia prahu detekcie podla osvetlenia
- Adaptivny ROI podla rychlosti
- Viacnasobne referencne obrazy
- Integrácia s inymi senzormi (ultrazvuk, LIDAR)

---

## Verzia 1.0 - 2025-01-29

### Pociatocna implementacia
- Zakladna integrácia motordriver a camera kniznic
- QR kod detekcia pre spustenie
- Beeper signalizacia
- Odpocitavanie 3 sekundy
- Jazda 10m rychlostou 10%
- Detekcia prekazky pomocou camera modulu
- Obchadzaci manever
- Graficke zobrazenie
- Podrobne logovanie
- Kompletna dokumentacia
