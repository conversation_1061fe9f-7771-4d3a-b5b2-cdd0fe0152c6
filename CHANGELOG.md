# Changelog - Kvalifikacny Program

## Verzia 1.1 - 2025-01-29

### Zlepsenia na zaklade testov

#### 1. Znizenie rychlosti pri otacacom manevri
**Problem**: <PERSON><PERSON><PERSON> r<PERSON> (30%) pri otacacom manevri sposobovali problemy
**Riesenie**: Zmenena rychlost otacania z 30% na 10% (rovnaka ako jazda)

```python
# Predtym:
self.robot.turn_right(30)  # 30% rychlost

# Teraz:
self.robot.turn_right(self.speed)  # 10% rychlost
```

**Vyhody**:
- Plynulejsie otacanie
- Mensie namahanie motorov
- Presnejsie manvre
- Konzistentna rychlost pocas celeho programu

#### 2. Inteligentna detekcia prekazky s referencnym obrazom
**Problem**: Detekcia prekazky nebola dostatocne presna
**Riesenie**: Implementacia referencneho obrazu po odpocitavani

**Novy algoritmus**:
1. <PERSON> nacitani QR kodu zacne odpocitavanie
2. Po 2 sekundach odpocitavania (pri poslednej sekunde) sa nastavi referencny obraz
3. Uzivatel ma cas zmiznut z kamery
4. Detekcia prekazky porovnava aktualny obraz s referencnym

```python
def set_reference_frame(self):
    """Nastavenie referencneho obrazu pre detekciu prekazok"""
    frame = self.camera.capture_frame()
    if frame is not None:
        self.reference_frame = frame.copy()
        self.reference_set = True
```

**Detekcia nahlej prekazky**:
- ROI (Region of Interest) v strede obrazu
- Porovnanie s referencnym obrazom
- Prah 15% zmeny pre detekciu prekazky
- Zameranie na vzdialenost 0-100cm

#### 3. Zabezpecenie zastavenia koles na konci
**Problem**: Kolesa sa stale tocili po ukonceni programu
**Riesenie**: Dvojite zastavenie s kratkou pauzou

```python
# Zastavenie na konci - zabezpecime ze sa kolesa zastavia
self.robot.stop_motors()
time.sleep(0.5)  # Kratke cakanie
self.robot.stop_motors()  # Druhe zastavenie pre istotu
```

**Vyhody**:
- Garantovane zastavenie motorov
- Bezpecnost po ukonceni programu
- Jasna signalizacia ukoncenia

### Technicke detaily

#### Detekcia prekazky - ROI
```python
# ROI v strede obrazu (stredna tretina horizontalne, spodna polovica vertikalne)
roi_x_start = width // 3
roi_x_end = 2 * width // 3
roi_y_start = height // 2
roi_y_end = height
```

#### Prah detekcie
```python
# Prah pre detekciu zmeny
threshold = 30  # Grayscale rozdiel
obstacle_threshold = 0.15  # 15% zmena pixelov
```

#### Graficke vylepšenia
- Vyznacenie ROI oblasti na obraze
- Indikator stavu referencneho obrazu
- Presnejsie informacie o detekcii prekazky

### Testovanie

#### Odporucany postup testovania
1. Spustit program
2. Ukazat QR kod
3. Pocas odpocitavania zmiznut z kamery (po 2 sekundach)
4. Sledovat detekciu prekazky v grafickom rozhrani
5. Testovat obchadzaci manever

#### Diagnostika
- ROI oblast je vyznacena zltym ramcekom
- Stav referencneho obrazu je zobrazeny v informaciach
- Detekcia prekazky je farebne oznacena

### Kompatibilita

Vsetky zmeny su spetne kompatibilne:
- Zachovane API motordriver a camera kniznic
- Zachovane konfiguracne parametre
- Zachovane logovanie a diagnostika

### Buduci vyvoj

Mozne dalsie vylepsenia:
- Kalibrácia prahu detekcie podla osvetlenia
- Adaptivny ROI podla rychlosti
- Viacnasobne referencne obrazy
- Integrácia s inymi senzormi (ultrazvuk, LIDAR)

---

## Verzia 1.0 - 2025-01-29

### Pociatocna implementacia
- Zakladna integrácia motordriver a camera kniznic
- QR kod detekcia pre spustenie
- Beeper signalizacia
- Odpocitavanie 3 sekundy
- Jazda 10m rychlostou 10%
- Detekcia prekazky pomocou camera modulu
- Obchadzaci manever
- Graficke zobrazenie
- Podrobne logovanie
- Kompletna dokumentacia
