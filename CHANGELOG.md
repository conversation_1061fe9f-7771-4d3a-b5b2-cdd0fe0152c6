# Changelog - Kvalifikacny Program

## Verzia 1.8 - 2025-01-29

### FINÁLNE DOLADENIE - VZDIALENOSŤ V CM

**ÚSPECH V V1.7:**
- Detekcia konečne funguje správne! ✅
- Robot sa zastavuje pri prekážkach ✅
- Čaká 10 sekúnd a pokračuje ✅

**POSLEDNÁ ÚPRAVA:**
- Detekcia bola príli<PERSON> blízko (5cm)
- Potreba bezpečnejš<PERSON> vzdialenosti

**RIEŠENIE:**
1. **Nastavenie vzdialenosti v cm** - namiesto percent
2. **Automatický prepočet citlivosti** - formula na základe vzdialenosti
3. **Užívateľsky prívetivé** - "50cm" namiesto "20%"
4. **Odporúčané hodnoty** - 5cm (blízko) až 100cm (ďaleko)

### Formula prepočtu
```python
# Empiricky testované hodnoty:
# 5cm = 35%, 10cm = 30%, 20cm = 25%, 50cm = 15%, 100cm = 5%
citlivost = max(5.0, 40.0 - (vzdialenost_cm * 0.35))
```

### Odporúčané nastavenia
- **5cm** - veľmi blízko (citlivosť 38.25%)
- **10cm** - blízko (citlivosť 36.5%)
- **20cm** - stredne (citlivosť 33%)
- **50cm** - ďaleko (citlivosť 22.5%) - DEFAULT
- **100cm** - veľmi ďaleko (citlivosť 5%)

## Verzia 1.7 - 2025-01-29

### KOMPLETNE PREPRACOVANÁ DETEKCIA - JEDNODUCHÁ A SPOĽAHLIVÁ

**PROBLÉM V V1.6:**
- Zložitá detekcia stále nefungovala správne
- Robot sa zastavoval bez dôvodu
- Príliš veľa falošných alarmov

**FINÁLNE RIEŠENIE - TESLA ŠTÝL:**
1. **Veľmi malá ROI** - len 20% šírky × 50% výšky v strede
2. **Jednoduchý algoritmus** - len detekcia tmavých oblastí
3. **Vysoký prah** - 40% ROI musí byť tmavé
4. **Minimálne logovanie** - len pri detekcii alebo každých 5s

### Nová jednoduchá detekcia
```python
def detect_simple_obstacle(self, frame):
    # Malá ROI v strede (20% × 50%)
    roi = frame[center_y_start:center_y_end, center_x_start:center_x_end]

    # Jednoduchý prah pre tmavé oblasti
    _, binary = cv2.threshold(gray_roi, 60, 255, cv2.THRESH_BINARY_INV)

    # Vysoký prah - 40% ROI musí byť tmavé
    coverage_percent = (dark_pixels / total_pixels) * 100
    return coverage_percent > 40.0
```

### Výhody novej detekcie
- **Spoľahlivá** - detekuje len veľké objekty priamo v ceste
- **Rýchla** - jednoduchý algoritmus
- **Presná** - malá ROI eliminuje falošné alarmy
- **Debugovateľná** - screenshoty s vyznačenou ROI

## Verzia 1.6 - 2025-01-29 (STÁLE PROBLEMATICKÁ)

### FINÁLNA OPRAVA - 3 SEKUNDY "NASLEPO"

**PROBLÉM V V1.5:**
- Detekcia sa stále zapínala hneď po štarte
- Robot detekoval zvyšky QR kódu alebo ruku
- Program sa ukončoval na začiatku jazdy

**RIEŠENIE (FUNGUJE, ALE DETEKCIA STÁLE ZLA):**
1. **3 sekundy "naslepo"** - prvé 3s bez detekcie prekážok
2. **Časovač od štartu** - `driving_start_time` + `BLIND_DRIVE_TIME`
3. **Postupné zapnutie** - detekcia sa zapne až po 3 sekundách
4. **Grafické zobrazenie** - "NASLEPO (2.3s)" → "AKTIVNA"

### Nová sekvencia detekcie
```
QR čakanie → DETEKCIA VYPNUTÁ
Odpočítavanie → DETEKCIA VYPNUTÁ
START! → JAZDA NASLEPO (3s)
Po 3s → DETEKCIA AKTIVNA
```

### Bezpečnostné opatrenia
- Robot ide dopredu prvé 3s bez kontroly prekážok
- Umožňuje "uniknúť" z oblasti QR kódu
- Po 3s sa zapne plná detekcia prekážok

## Verzia 1.5 - 2025-01-29 (STÁLE PROBLEMATICKÁ)

### OPRAVA PROBLÉMU S PREDČASNOU DETEKCIOU

**PROBLÉM V V1.4:**
- Detekcia prekážok bežala už počas čakania na QR kód
- Robot detekoval ruku s QR kódom ako prekážku
- Program sa ukončoval pred začiatkom jazdy

**POKUS O RIEŠENIE (NESTAČILO):**
1. **Detekcia prekážok sa zapína až po štarte** - flag `driving_started`
2. **Počas QR čakania a odpočítavania** - detekcia vypnutá
3. **Po "START!"** - detekcia sa zapne (PRÍLIŠ SKORO)
4. **Grafické zobrazenie** - indikátor "ZAPNUTÁ/VYPNUTÁ"

## Verzia 1.4 - 2025-01-29

### KRITICKÁ OPRAVA BEZPEČNOSTI - Vylepšená detekcia prekážok

**BEZPEČNOSTNÝ PROBLÉM:**
- Predošlá detekcia prekážok nefungovala správne
- Robot pokračoval v jazde aj pri prekážkach
- Riziko úrazu alebo poškodenia

**RIEŠENIE:**
1. **Kompletne prepracovaná detekcia** - kombinuje detekciu tmavých oblastí + hrany
2. **Podrobné logovanie** - každá detekcia je zalogovaná s percentami
3. **Automatické screenshoty** - ukladanie snímok pred/po prekážke do logs/ adresára
4. **Núdzové zastavenie** - pri veľmi veľkých prekážkach (>50%) okamžité ukončenie
5. **Dvojité zastavenie motorov** - pre garantované zastavenie

### Nová detekcia prekážok
```python
def detect_large_obstacle(self, frame):
    # Metoda 1: Detekcia tmavých oblastí
    _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
    dark_percentage = (dark_pixels / total_pixels) * 100

    # Metoda 2: Detekcia hrán
    edges = cv2.Canny(blurred, 50, 150)
    edge_percentage = (edge_pixels / total_pixels) * 100

    # Kombinacia - berieme vyššie percento
    combined_percentage = max(dark_percentage, edge_percentage)
    return combined_percentage > threshold
```

### Bezpečnostné funkcie
- **Okamžité zastavenie** pri detekcii prekážky
- **Dvojité zastavenie motorov** pre istotu
- **Núdzové zastavenie** pri >50% pokrytí obrazu
- **Kontinuálne monitorovanie** počas čakania
- **Automatické screenshoty** pre analýzu

## Verzia 1.3 - 2025-01-29 (NEBEZPEČNÁ - OPRAVENÁ)

### Zlepsenie detekcie prekazky a zrusenie obchadzania

**Problemy v predoslych verziach:**
- Robot sa zastavoval prilis casto kvoli citlivej detekcii prekazky
- Objekty sa priblizovali ku kamere a spustali falošne alarmy
- Obchadzaci manever bol komplikovany a nepotrebny

**Pokus o riesenie (NEFUNGOVALO):**
1. **Nova detekcia velkej prekazky** - detekuje len prekazky, ktore zaberaju viac ako X% obrazovej plochy
2. **Konfigurovatelny prah** - moznost nastavit prah detekcie (default 20%)
3. **Zrusenie obchadzania** - robot len caka 10s a potom pokracuje dopredu
4. **Lepsia vizualizacia** - zobrazenie prahu detekcie v grafickom rozhrani

### Technicke detaily

#### Nova detekcia prekazky
```python
def detect_large_obstacle(self, frame):
    # Konverzia na grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Detekcia hran pomocou Canny
    edges = cv2.Canny(blurred, 50, 150)

    # Vypocet percenta obrazu s hranami
    edge_percentage = (edge_pixels / total_pixels) * 100

    # Kontrola ci prekazka zabera viac ako threshold %
    return edge_percentage > self.obstacle_threshold_percent
```

#### Konfigurovatelny prah
- Moznost nastavit pri spusteni programu
- Default hodnota: 20%
- Rozsah: 5% - 50% (odporucane)

#### Zjednodusena logika
- Pri detekcii prekazky: zastav na 10s
- Po 10s: pokracuj v jazde bez ohladu na prekazku
- Ziadne obchadzanie ani komplikovane manvre

## Verzia 1.2 - 2025-01-29

### Oprava problemov z verzie 1.1

**Problemy v v1.1:**
- Detekcia prekazky nefungovala spravne (robot nejde dopredu)
- Divna kombinacia stavov (forward + prekazka + motor stoji)
- Komplikovana detekcia referencneho obrazu sposobovala problemy

**Riesenie:**
- Navrat k povodnej funkcnej detekcii prekazky z camera modulu
- Zachovanie len potrebnych zmien (rychlost otacania + zastavenie koles)
- Odstranenie problematickej detekcie referencneho obrazu

## Verzia 1.1 - 2025-01-29 (PROBLEMATICKA - OPRAVENA)

### Zlepsenia na zaklade testov

#### 1. Znizenie rychlosti pri otacacom manevri
**Problem**: Vysoke rychlosti (30%) pri otacacom manevri sposobovali problemy
**Riesenie**: Zmenena rychlost otacania z 30% na 10% (rovnaka ako jazda)

```python
# Predtym:
self.robot.turn_right(30)  # 30% rychlost

# Teraz:
self.robot.turn_right(self.speed)  # 10% rychlost
```

**Vyhody**:
- Plynulejsie otacanie
- Mensie namahanie motorov
- Presnejsie manvre
- Konzistentna rychlost pocas celeho programu
- ✅ **ZACHOVANE v v1.2**

#### 2. Zabezpecenie zastavenia koles na konci
**Problem**: Kolesa sa stale tocili po ukonceni programu
**Riesenie**: Dvojite zastavenie s kratkou pauzou

```python
# Zastavenie na konci - zabezpecime ze sa kolesa zastavia
self.robot.stop_motors()
time.sleep(0.5)  # Kratke cakanie
self.robot.stop_motors()  # Druhe zastavenie pre istotu
```

**Vyhody**:
- Garantovane zastavenie motorov
- Bezpecnost po ukonceni programu
- Jasna signalizacia ukoncenia
- ✅ **ZACHOVANE v v1.2**

#### 3. Inteligentna detekcia prekazky s referencnym obrazom (ODSTRANENE)
**Problem**: Detekcia prekazky nebola dostatocne presna
**Pokus o riesenie**: Implementacia referencneho obrazu po odpocitavani
**Vysledok**: ❌ **NEFUNGOVALO - robot sa nehyboval, detekcia bola prilis citliva**

**Novy algoritmus**:
1. Po nacitani QR kodu zacne odpocitavanie
2. Po 2 sekundach odpocitavania (pri poslednej sekunde) sa nastavi referencny obraz
3. Uzivatel ma cas zmiznut z kamery
4. Detekcia prekazky porovnava aktualny obraz s referencnym

```python
def set_reference_frame(self):
    """Nastavenie referencneho obrazu pre detekciu prekazok"""
    frame = self.camera.capture_frame()
    if frame is not None:
        self.reference_frame = frame.copy()
        self.reference_set = True
```

**Detekcia nahlej prekazky**:
- ROI (Region of Interest) v strede obrazu
- Porovnanie s referencnym obrazom
- Prah 15% zmeny pre detekciu prekazky
- Zameranie na vzdialenost 0-100cm

#### 3. Zabezpecenie zastavenia koles na konci
**Problem**: Kolesa sa stale tocili po ukonceni programu
**Riesenie**: Dvojite zastavenie s kratkou pauzou

```python
# Zastavenie na konci - zabezpecime ze sa kolesa zastavia
self.robot.stop_motors()
time.sleep(0.5)  # Kratke cakanie
self.robot.stop_motors()  # Druhe zastavenie pre istotu
```

**Vyhody**:
- Garantovane zastavenie motorov
- Bezpecnost po ukonceni programu
- Jasna signalizacia ukoncenia

### Technicke detaily

#### Detekcia prekazky - ROI
```python
# ROI v strede obrazu (stredna tretina horizontalne, spodna polovica vertikalne)
roi_x_start = width // 3
roi_x_end = 2 * width // 3
roi_y_start = height // 2
roi_y_end = height
```

#### Prah detekcie
```python
# Prah pre detekciu zmeny
threshold = 30  # Grayscale rozdiel
obstacle_threshold = 0.15  # 15% zmena pixelov
```

#### Graficke vylepšenia
- Vyznacenie ROI oblasti na obraze
- Indikator stavu referencneho obrazu
- Presnejsie informacie o detekcii prekazky

### Testovanie

#### Odporucany postup testovania
1. Spustit program
2. Ukazat QR kod
3. Pocas odpocitavania zmiznut z kamery (po 2 sekundach)
4. Sledovat detekciu prekazky v grafickom rozhrani
5. Testovat obchadzaci manever

#### Diagnostika
- ROI oblast je vyznacena zltym ramcekom
- Stav referencneho obrazu je zobrazeny v informaciach
- Detekcia prekazky je farebne oznacena

### Kompatibilita

Vsetky zmeny su spetne kompatibilne:
- Zachovane API motordriver a camera kniznic
- Zachovane konfiguracne parametre
- Zachovane logovanie a diagnostika

### Buduci vyvoj

Mozne dalsie vylepsenia:
- Kalibrácia prahu detekcie podla osvetlenia
- Adaptivny ROI podla rychlosti
- Viacnasobne referencne obrazy
- Integrácia s inymi senzormi (ultrazvuk, LIDAR)

---

## Verzia 1.0 - 2025-01-29

### Pociatocna implementacia
- Zakladna integrácia motordriver a camera kniznic
- QR kod detekcia pre spustenie
- Beeper signalizacia
- Odpocitavanie 3 sekundy
- Jazda 10m rychlostou 10%
- Detekcia prekazky pomocou camera modulu
- Obchadzaci manever
- Graficke zobrazenie
- Podrobne logovanie
- Kompletna dokumentacia
