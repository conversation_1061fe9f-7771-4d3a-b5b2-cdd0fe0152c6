#!/usr/bin/env python3
"""
OpenStreetMap Module for Robotic Vehicle
Loads map, plans route to goal coordinates.
Suggests turn-by-turn navigation commands.
"""

import threading
import time
import math
import json
from queue import Empty
from typing import Dict, List, Tuple, Optional


class OSMModule:
    """OpenStreetMap module for route planning and navigation."""
    
    def __init__(self):
        """Initialize the OSM module."""
        self.module_name = "osm"
        
        # Map data
        self.map_data = {}
        self.map_bounds = {
            'min_lat': 0.0, 'max_lat': 0.0,
            'min_lon': 0.0, 'max_lon': 0.0
        }
        self.map_loaded = False
        
        # Route planning
        self.current_route = []
        self.route_waypoints = []
        self.current_waypoint_index = 0
        self.destination = None
        self.route_active = False
        
        # Navigation state
        self.next_waypoint = None
        self.distance_to_waypoint = 0.0
        self.bearing_to_waypoint = 0.0
        self.navigation_instructions = []
        
        # Configuration
        self.waypoint_tolerance = 5.0  # meters - distance to consider waypoint reached
        self.route_recalc_distance = 50.0  # meters - distance threshold for route recalculation
        self.max_route_deviation = 20.0  # meters - max allowed deviation from route
        
        # Simulation mode for testing
        self.simulation_mode = True
        self.sim_map_area = {
            'center_lat': 49.2827,  # Vancouver coordinates
            'center_lon': -123.1207,
            'radius': 0.01  # degrees (~1km)
        }
        
        # OSM status
        self.osm_faults = []
        self.last_route_calculation = 0
        
        # Thread control
        self.running = False
        
    def load_map_data(self, bounds: Dict) -> bool:
        """Load map data for the specified bounds."""
        try:
            if self.simulation_mode:
                return self.simulate_map_loading(bounds)
            else:
                # Real map loading would go here
                # This would involve downloading OSM data using APIs like:
                # - Overpass API
                # - OSM XML parsing
                # - Graph construction for routing
                return self.load_real_map_data(bounds)
                
        except Exception as e:
            self.add_fault(f"Error loading map data: {e}")
            return False
    
    def simulate_map_loading(self, bounds: Dict) -> bool:
        """Simulate map loading for testing."""
        try:
            # Create a simple simulated map with some waypoints
            center_lat = self.sim_map_area['center_lat']
            center_lon = self.sim_map_area['center_lon']
            radius = self.sim_map_area['radius']
            
            # Generate some sample waypoints in a grid pattern
            waypoints = []
            for i in range(-2, 3):
                for j in range(-2, 3):
                    lat = center_lat + (i * radius / 4)
                    lon = center_lon + (j * radius / 4)
                    waypoints.append({
                        'id': f"waypoint_{i}_{j}",
                        'lat': lat,
                        'lon': lon,
                        'type': 'intersection'
                    })
            
            self.map_data = {
                'waypoints': waypoints,
                'roads': [],  # Simplified - would contain road segments
                'bounds': bounds
            }
            
            self.map_bounds = bounds
            self.map_loaded = True
            return True
            
        except Exception as e:
            self.add_fault(f"Error in simulated map loading: {e}")
            return False
    
    def load_real_map_data(self, bounds: Dict) -> bool:
        """Load real OSM data (placeholder for actual implementation)."""
        # This would contain actual OSM data loading logic
        # For example, using libraries like:
        # - osmnx for road network data
        # - overpy for Overpass API queries
        # - networkx for graph-based routing
        
        # Placeholder implementation
        self.map_loaded = False
        return False
    
    def calculate_route(self, start: Dict, destination: Dict) -> List[Dict]:
        """Calculate route from start to destination."""
        try:
            if not self.map_loaded:
                self.add_fault("Cannot calculate route: map not loaded")
                return []
            
            if self.simulation_mode:
                return self.simulate_route_calculation(start, destination)
            else:
                return self.calculate_real_route(start, destination)
                
        except Exception as e:
            self.add_fault(f"Error calculating route: {e}")
            return []
    
    def simulate_route_calculation(self, start: Dict, destination: Dict) -> List[Dict]:
        """Simulate route calculation for testing."""
        try:
            start_lat = start['latitude']
            start_lon = start['longitude']
            dest_lat = destination['latitude']
            dest_lon = destination['longitude']
            
            # Create a simple route with intermediate waypoints
            route = []
            
            # Add start point
            route.append({
                'latitude': start_lat,
                'longitude': start_lon,
                'instruction': 'Start navigation',
                'distance': 0.0,
                'bearing': 0.0
            })
            
            # Add some intermediate waypoints (simplified straight-line route)
            num_segments = 5
            for i in range(1, num_segments):
                progress = i / num_segments
                lat = start_lat + (dest_lat - start_lat) * progress
                lon = start_lon + (dest_lon - start_lon) * progress
                
                # Calculate distance and bearing to this waypoint
                distance = self.calculate_distance(start_lat, start_lon, lat, lon)
                bearing = self.calculate_bearing(start_lat, start_lon, lat, lon)
                
                route.append({
                    'latitude': lat,
                    'longitude': lon,
                    'instruction': f'Continue for {distance:.0f}m',
                    'distance': distance,
                    'bearing': bearing
                })
            
            # Add destination
            total_distance = self.calculate_distance(start_lat, start_lon, dest_lat, dest_lon)
            final_bearing = self.calculate_bearing(start_lat, start_lon, dest_lat, dest_lon)
            
            route.append({
                'latitude': dest_lat,
                'longitude': dest_lon,
                'instruction': 'Arrive at destination',
                'distance': total_distance,
                'bearing': final_bearing
            })
            
            return route
            
        except Exception as e:
            self.add_fault(f"Error in simulated route calculation: {e}")
            return []
    
    def calculate_real_route(self, start: Dict, destination: Dict) -> List[Dict]:
        """Calculate real route using OSM data (placeholder)."""
        # This would contain actual routing algorithms like:
        # - Dijkstra's algorithm
        # - A* pathfinding
        # - Turn restrictions handling
        # - Road type preferences
        
        # Placeholder implementation
        return []
    
    def update_navigation(self, current_position: Dict):
        """Update navigation based on current position."""
        try:
            if not self.route_active or not self.current_route:
                return
            
            current_lat = current_position['latitude']
            current_lon = current_position['longitude']
            
            # Check if we've reached the current waypoint
            if self.current_waypoint_index < len(self.current_route):
                waypoint = self.current_route[self.current_waypoint_index]
                distance = self.calculate_distance(
                    current_lat, current_lon,
                    waypoint['latitude'], waypoint['longitude']
                )
                
                if distance <= self.waypoint_tolerance:
                    # Move to next waypoint
                    self.current_waypoint_index += 1
                    
                    if self.current_waypoint_index >= len(self.current_route):
                        # Route completed
                        self.route_active = False
                        self.next_waypoint = None
                        return
                
                # Update next waypoint information
                if self.current_waypoint_index < len(self.current_route):
                    next_wp = self.current_route[self.current_waypoint_index]
                    self.next_waypoint = next_wp
                    self.distance_to_waypoint = self.calculate_distance(
                        current_lat, current_lon,
                        next_wp['latitude'], next_wp['longitude']
                    )
                    self.bearing_to_waypoint = self.calculate_bearing(
                        current_lat, current_lon,
                        next_wp['latitude'], next_wp['longitude']
                    )
            
        except Exception as e:
            self.add_fault(f"Error updating navigation: {e}")
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two coordinates using Haversine formula."""
        try:
            # Convert to radians
            lat1_rad = math.radians(lat1)
            lon1_rad = math.radians(lon1)
            lat2_rad = math.radians(lat2)
            lon2_rad = math.radians(lon2)
            
            # Haversine formula
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            
            a = (math.sin(dlat/2)**2 + 
                 math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
            c = 2 * math.asin(math.sqrt(a))
            
            # Earth radius in meters
            earth_radius = 6371000
            distance = earth_radius * c
            
            return distance
            
        except Exception as e:
            self.add_fault(f"Error calculating distance: {e}")
            return 0.0
    
    def calculate_bearing(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate bearing from point 1 to point 2."""
        try:
            lat1_rad = math.radians(lat1)
            lat2_rad = math.radians(lat2)
            dlon_rad = math.radians(lon2 - lon1)
            
            y = math.sin(dlon_rad) * math.cos(lat2_rad)
            x = (math.cos(lat1_rad) * math.sin(lat2_rad) - 
                 math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon_rad))
            
            bearing_rad = math.atan2(y, x)
            bearing_deg = math.degrees(bearing_rad)
            
            # Normalize to 0-360 degrees
            bearing_deg = (bearing_deg + 360) % 360
            
            return bearing_deg
            
        except Exception as e:
            self.add_fault(f"Error calculating bearing: {e}")
            return 0.0
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.osm_faults.append(fault)
        
        # Keep only recent faults (last 50)
        if len(self.osm_faults) > 50:
            self.osm_faults = self.osm_faults[-50:]
    
    def get_status(self) -> Dict:
        """Get current OSM status."""
        status = 'running' if self.map_loaded else 'no_map'
        if self.osm_faults:
            status = 'error'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'map_loaded': self.map_loaded,
                'route_active': self.route_active,
                'route': self.current_route.copy() if self.current_route else None,
                'next_waypoint': self.next_waypoint,
                'distance_to_waypoint': self.distance_to_waypoint,
                'bearing_to_waypoint': self.bearing_to_waypoint,
                'current_waypoint_index': self.current_waypoint_index,
                'destination': self.destination,
                'faults': self.osm_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'load_map':
                bounds = command.get('bounds')
                if bounds:
                    self.load_map_data(bounds)
            elif action == 'set_destination':
                destination = command.get('destination')
                current_position = command.get('current_position')
                if destination and current_position:
                    self.destination = destination
                    route = self.calculate_route(current_position, destination)
                    if route:
                        self.current_route = route
                        self.current_waypoint_index = 0
                        self.route_active = True
                        self.last_route_calculation = time.time()
            elif action == 'cancel_route':
                self.route_active = False
                self.current_route = []
                self.next_waypoint = None
            elif action == 'reset_faults':
                self.osm_faults.clear()
            else:
                self.add_fault(f"Unknown OSM command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'OSM module started',
            'timestamp': time.time()
        })
        
        # Load initial map data
        if self.simulation_mode:
            initial_bounds = {
                'min_lat': self.sim_map_area['center_lat'] - self.sim_map_area['radius'],
                'max_lat': self.sim_map_area['center_lat'] + self.sim_map_area['radius'],
                'min_lon': self.sim_map_area['center_lon'] - self.sim_map_area['radius'],
                'max_lon': self.sim_map_area['center_lon'] + self.sim_map_area['radius']
            }
            self.load_map_data(initial_bounds)
        
        last_status_time = 0
        status_interval = 1.0  # Send status every second
        
        # Store current position for navigation updates
        current_position = None
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                        elif command.get('module') == 'gps' and command.get('action') == 'position_update':
                            # Listen for GPS position updates
                            current_position = command.get('position')
                except Empty:
                    pass
                
                # Update navigation if we have a current position and active route
                if current_position and self.route_active:
                    self.update_navigation(current_position)
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.1)
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'OSM module error: {e}',
                'timestamp': time.time()
            })
        finally:
            self.running = False
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'OSM module stopped',
                'timestamp': time.time()
            })
