#!/usr/bin/env python3
"""
Shutdown Module for Robotic Vehicle
Uses an Event object to coordinate clean stop across threads.
Triggerable by error conditions or user command.
"""

import threading
import time
import signal
import os
from queue import Empty
from typing import Dict, List, Optional, Callable


class ShutdownModule:
    """Shutdown coordination module for graceful system termination."""
    
    def __init__(self):
        """Initialize the shutdown module."""
        self.module_name = "shutdown"
        
        # Shutdown coordination
        self.shutdown_initiated = False
        self.shutdown_reason = ""
        self.shutdown_timestamp = 0.0
        self.shutdown_timeout = 30.0  # seconds to wait for graceful shutdown
        
        # Module shutdown tracking
        self.modules_to_shutdown = [
            'motors', 'gps', 'vision', 'osm', 'watchdog', 'logging', 'communication'
        ]
        self.module_shutdown_status = {}
        self.module_shutdown_timeouts = {}
        self.module_shutdown_timeout = 10.0  # seconds per module
        
        # Shutdown phases
        self.shutdown_phases = [
            'prepare',      # Prepare for shutdown (save data, stop navigation)
            'stop_motors',  # Stop all motor activity
            'stop_sensors', # Stop sensor modules
            'stop_support', # Stop support modules (logging, communication)
            'cleanup'       # Final cleanup
        ]
        self.current_phase = None
        self.phase_start_time = 0.0
        
        # Shutdown triggers
        self.shutdown_triggers = {
            'user_request': False,
            'emergency_stop': False,
            'system_error': False,
            'watchdog_timeout': False,
            'signal_received': False
        }
        
        # Emergency shutdown
        self.emergency_shutdown = False
        self.emergency_timeout = 5.0  # seconds for emergency shutdown
        
        # Shutdown callbacks
        self.pre_shutdown_callbacks = []
        self.post_shutdown_callbacks = []
        
        # Shutdown status
        self.shutdown_faults = []
        self.shutdown_complete = False
        
        # Thread control
        self.running = False
        
    def initialize_shutdown_system(self):
        """Initialize shutdown coordination system."""
        try:
            # Initialize module shutdown tracking
            for module in self.modules_to_shutdown:
                self.module_shutdown_status[module] = {
                    'requested': False,
                    'acknowledged': False,
                    'completed': False,
                    'timeout': False,
                    'request_time': 0.0
                }
                self.module_shutdown_timeouts[module] = self.module_shutdown_timeout
            
            # Set up signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            return True
            
        except Exception as e:
            self.add_fault(f"Error initializing shutdown system: {e}")
            return False
    
    def _signal_handler(self, signum, frame):
        """Handle system signals for shutdown."""
        signal_names = {
            signal.SIGINT: 'SIGINT',
            signal.SIGTERM: 'SIGTERM'
        }
        signal_name = signal_names.get(signum, f'Signal {signum}')
        
        self.initiate_shutdown(
            reason=f"System signal received: {signal_name}",
            trigger='signal_received'
        )
    
    def register_pre_shutdown_callback(self, callback: Callable):
        """Register a callback to be called before shutdown begins."""
        if callable(callback):
            self.pre_shutdown_callbacks.append(callback)
    
    def register_post_shutdown_callback(self, callback: Callable):
        """Register a callback to be called after shutdown completes."""
        if callable(callback):
            self.post_shutdown_callbacks.append(callback)
    
    def initiate_shutdown(self, reason: str, trigger: str = 'user_request', 
                         emergency: bool = False):
        """Initiate system shutdown."""
        try:
            if self.shutdown_initiated:
                return  # Shutdown already in progress
            
            self.shutdown_initiated = True
            self.shutdown_reason = reason
            self.shutdown_timestamp = time.time()
            self.emergency_shutdown = emergency
            
            # Set trigger
            if trigger in self.shutdown_triggers:
                self.shutdown_triggers[trigger] = True
            
            # Execute pre-shutdown callbacks
            self.execute_pre_shutdown_callbacks()
            
            # Start shutdown process
            if emergency:
                self.current_phase = 'emergency'
                self.phase_start_time = time.time()
            else:
                self.current_phase = 'prepare'
                self.phase_start_time = time.time()
            
            self.add_fault(f"Shutdown initiated: {reason} (emergency: {emergency})")
            
        except Exception as e:
            self.add_fault(f"Error initiating shutdown: {e}")
    
    def execute_pre_shutdown_callbacks(self):
        """Execute all registered pre-shutdown callbacks."""
        try:
            for callback in self.pre_shutdown_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.add_fault(f"Error in pre-shutdown callback: {e}")
                    
        except Exception as e:
            self.add_fault(f"Error executing pre-shutdown callbacks: {e}")
    
    def execute_post_shutdown_callbacks(self):
        """Execute all registered post-shutdown callbacks."""
        try:
            for callback in self.post_shutdown_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.add_fault(f"Error in post-shutdown callback: {e}")
                    
        except Exception as e:
            self.add_fault(f"Error executing post-shutdown callbacks: {e}")
    
    def process_shutdown_phase(self):
        """Process current shutdown phase."""
        try:
            if not self.shutdown_initiated:
                return
            
            current_time = time.time()
            phase_duration = current_time - self.phase_start_time
            
            if self.emergency_shutdown:
                self.process_emergency_shutdown()
                return
            
            # Process normal shutdown phases
            if self.current_phase == 'prepare':
                self.process_prepare_phase()
            elif self.current_phase == 'stop_motors':
                self.process_stop_motors_phase()
            elif self.current_phase == 'stop_sensors':
                self.process_stop_sensors_phase()
            elif self.current_phase == 'stop_support':
                self.process_stop_support_phase()
            elif self.current_phase == 'cleanup':
                self.process_cleanup_phase()
            
            # Check for phase timeout
            if phase_duration > self.shutdown_timeout:
                self.add_fault(f"Shutdown phase '{self.current_phase}' timed out")
                self.advance_to_next_phase()
                
        except Exception as e:
            self.add_fault(f"Error processing shutdown phase: {e}")
    
    def process_prepare_phase(self):
        """Process preparation phase of shutdown."""
        try:
            # Send preparation commands to modules
            prepare_modules = ['osm', 'vision', 'logging']
            
            for module in prepare_modules:
                if not self.module_shutdown_status[module]['requested']:
                    self.request_module_shutdown(module, 'prepare')
            
            # Check if all modules have acknowledged preparation
            if self.all_modules_acknowledged(prepare_modules):
                self.advance_to_next_phase()
                
        except Exception as e:
            self.add_fault(f"Error in prepare phase: {e}")
    
    def process_stop_motors_phase(self):
        """Process motor stopping phase."""
        try:
            # Stop motors first for safety
            if not self.module_shutdown_status['motors']['requested']:
                self.request_module_shutdown('motors', 'emergency_stop')
            
            # Wait for motors to stop
            if self.module_shutdown_status['motors']['completed']:
                self.advance_to_next_phase()
                
        except Exception as e:
            self.add_fault(f"Error in stop motors phase: {e}")
    
    def process_stop_sensors_phase(self):
        """Process sensor stopping phase."""
        try:
            sensor_modules = ['gps', 'vision']
            
            for module in sensor_modules:
                if not self.module_shutdown_status[module]['requested']:
                    self.request_module_shutdown(module, 'shutdown')
            
            # Check if all sensor modules have stopped
            if self.all_modules_completed(sensor_modules):
                self.advance_to_next_phase()
                
        except Exception as e:
            self.add_fault(f"Error in stop sensors phase: {e}")
    
    def process_stop_support_phase(self):
        """Process support module stopping phase."""
        try:
            support_modules = ['osm', 'communication', 'watchdog']
            
            for module in support_modules:
                if not self.module_shutdown_status[module]['requested']:
                    self.request_module_shutdown(module, 'shutdown')
            
            # Check if all support modules have stopped
            if self.all_modules_completed(support_modules):
                self.advance_to_next_phase()
                
        except Exception as e:
            self.add_fault(f"Error in stop support phase: {e}")
    
    def process_cleanup_phase(self):
        """Process final cleanup phase."""
        try:
            # Stop logging last
            if not self.module_shutdown_status['logging']['requested']:
                self.request_module_shutdown('logging', 'shutdown')
            
            # Final cleanup
            if self.module_shutdown_status['logging']['completed']:
                self.complete_shutdown()
                
        except Exception as e:
            self.add_fault(f"Error in cleanup phase: {e}")
    
    def process_emergency_shutdown(self):
        """Process emergency shutdown."""
        try:
            current_time = time.time()
            
            # Request immediate shutdown of all modules
            for module in self.modules_to_shutdown:
                if not self.module_shutdown_status[module]['requested']:
                    self.request_module_shutdown(module, 'emergency_shutdown')
            
            # Check timeout
            if current_time - self.phase_start_time > self.emergency_timeout:
                self.complete_shutdown()
                
        except Exception as e:
            self.add_fault(f"Error in emergency shutdown: {e}")
    
    def request_module_shutdown(self, module: str, shutdown_type: str):
        """Request shutdown of a specific module."""
        try:
            current_time = time.time()
            
            # Update tracking
            self.module_shutdown_status[module]['requested'] = True
            self.module_shutdown_status[module]['request_time'] = current_time
            
            # In a real implementation, this would send shutdown command
            # to the module via command queue
            # For now, we'll simulate the request
            
        except Exception as e:
            self.add_fault(f"Error requesting shutdown for {module}: {e}")
    
    def acknowledge_module_shutdown(self, module: str):
        """Acknowledge that a module has received shutdown request."""
        if module in self.module_shutdown_status:
            self.module_shutdown_status[module]['acknowledged'] = True
    
    def complete_module_shutdown(self, module: str):
        """Mark a module as completely shut down."""
        if module in self.module_shutdown_status:
            self.module_shutdown_status[module]['completed'] = True
    
    def all_modules_acknowledged(self, modules: List[str]) -> bool:
        """Check if all specified modules have acknowledged shutdown."""
        for module in modules:
            if not self.module_shutdown_status.get(module, {}).get('acknowledged', False):
                return False
        return True
    
    def all_modules_completed(self, modules: List[str]) -> bool:
        """Check if all specified modules have completed shutdown."""
        for module in modules:
            if not self.module_shutdown_status.get(module, {}).get('completed', False):
                return False
        return True
    
    def advance_to_next_phase(self):
        """Advance to the next shutdown phase."""
        try:
            if self.current_phase == 'prepare':
                self.current_phase = 'stop_motors'
            elif self.current_phase == 'stop_motors':
                self.current_phase = 'stop_sensors'
            elif self.current_phase == 'stop_sensors':
                self.current_phase = 'stop_support'
            elif self.current_phase == 'stop_support':
                self.current_phase = 'cleanup'
            elif self.current_phase == 'cleanup':
                self.complete_shutdown()
                return
            
            self.phase_start_time = time.time()
            
        except Exception as e:
            self.add_fault(f"Error advancing shutdown phase: {e}")
    
    def complete_shutdown(self):
        """Complete the shutdown process."""
        try:
            self.shutdown_complete = True
            self.current_phase = 'complete'
            
            # Execute post-shutdown callbacks
            self.execute_post_shutdown_callbacks()
            
            # Final log message
            duration = time.time() - self.shutdown_timestamp
            self.add_fault(f"Shutdown completed in {duration:.2f} seconds")
            
        except Exception as e:
            self.add_fault(f"Error completing shutdown: {e}")
    
    def check_module_timeouts(self):
        """Check for module shutdown timeouts."""
        try:
            current_time = time.time()
            
            for module, status in self.module_shutdown_status.items():
                if (status['requested'] and not status['completed'] and 
                    not status['timeout']):
                    
                    elapsed = current_time - status['request_time']
                    timeout = self.module_shutdown_timeouts[module]
                    
                    if elapsed > timeout:
                        status['timeout'] = True
                        self.add_fault(f"Module {module} shutdown timeout ({elapsed:.1f}s)")
                        
        except Exception as e:
            self.add_fault(f"Error checking module timeouts: {e}")
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.shutdown_faults.append(fault)
        
        # Keep only recent faults (last 50)
        if len(self.shutdown_faults) > 50:
            self.shutdown_faults = self.shutdown_faults[-50:]
    
    def get_status(self) -> Dict:
        """Get current shutdown status."""
        status = 'ready'
        if self.shutdown_complete:
            status = 'complete'
        elif self.shutdown_initiated:
            status = f'shutting_down_{self.current_phase}'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'shutdown_initiated': self.shutdown_initiated,
                'shutdown_complete': self.shutdown_complete,
                'emergency_shutdown': self.emergency_shutdown,
                'current_phase': self.current_phase,
                'shutdown_reason': self.shutdown_reason,
                'shutdown_timestamp': self.shutdown_timestamp,
                'module_status': self.module_shutdown_status.copy(),
                'triggers': self.shutdown_triggers.copy(),
                'faults': self.shutdown_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'initiate_shutdown':
                reason = command.get('reason', 'User requested shutdown')
                emergency = command.get('emergency', False)
                self.initiate_shutdown(reason, 'user_request', emergency)
            elif action == 'acknowledge_shutdown':
                module = command.get('module')
                if module:
                    self.acknowledge_module_shutdown(module)
            elif action == 'complete_shutdown':
                module = command.get('module')
                if module:
                    self.complete_module_shutdown(module)
            elif action == 'reset_faults':
                self.shutdown_faults.clear()
            else:
                self.add_fault(f"Unknown shutdown command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'Shutdown module started',
            'timestamp': time.time()
        })
        
        # Initialize shutdown system
        if not self.initialize_shutdown_system():
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': 'Failed to initialize shutdown system',
                'timestamp': time.time()
            })
        
        last_status_time = 0
        status_interval = 2.0  # Send status every 2 seconds
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Process shutdown phases
                if self.shutdown_initiated and not self.shutdown_complete:
                    self.process_shutdown_phase()
                    self.check_module_timeouts()
                
                # Check if shutdown event is set
                if shutdown_event.is_set() and not self.shutdown_initiated:
                    self.initiate_shutdown("Shutdown event triggered", "system_event")
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.1)
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'Shutdown module error: {e}',
                'timestamp': time.time()
            })
        finally:
            self.running = False
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'Shutdown module stopped',
                'timestamp': time.time()
            })
