# Robotour Vehicle Control System

A modular, threaded Python system for autonomous robotic vehicle control, designed for the Robotour competition. The system emphasizes safety, reliability, and modular validation through independent threaded modules.

## System Architecture

The system consists of a main controller that orchestrates multiple independent modules running in separate threads:

```
┌─────────────────┐
│     main.py     │  ← Main Controller & Decision Loop
│   (Main Loop)   │
└─────────┬───────┘
          │
    ┌─────┴─────┐
    │  Modules  │
    └─────┬─────┘
          │
┌─────────┼─────────┐
│         │         │
▼         ▼         ▼
motors.py gps.py   vision.py
osm.py    watchdog.py  logging_module.py
communication.py  shutdown.py
```

## Core Modules

### 1. Main Controller (`main.py`)
- Initializes and manages all system modules
- Launches each module in its own thread
- Monitors system health and orchestrates graceful shutdowns
- Implements main decision loop: collects status, makes decisions, issues commands
- Handles emergency stop procedures

### 2. Motors Module (`motors.py`)
- Controls motor speed, direction, acceleration, and braking
- Receives commands via thread-safe queue from main controller
- Provides position tracking through simplified odometry
- Implements safety features: acceleration limiting, command timeouts
- Emits status (position, faults) back to main controller

### 3. GPS Module (`gps.py`)
- Fetches real-time geolocation data
- Provides location updates to main and other modules
- Includes simulation mode for testing without hardware
- Validates GPS data quality and accuracy
- Handles GPS signal loss and recovery

### 4. Vision Module (`vision.py`)
- Processes camera input for obstacle detection
- Ensures pedestrian safety and collision avoidance
- Analyzes path safety and recommends actions
- Supports both real camera input and simulation mode
- Provides emergency stop recommendations

### 5. OpenStreetMap Module (`osm.py`)
- Loads map data and plans routes to goal coordinates
- Provides turn-by-turn navigation commands
- Calculates distances and bearings between waypoints
- Supports both real OSM data and simulation mode
- Updates navigation based on current position

### 6. Watchdog Module (`watchdog.py`)
- Monitors active threads and system heartbeat
- Tracks system resource usage (CPU, memory, disk)
- Restarts unresponsive modules or triggers safe shutdown
- Implements emergency shutdown for critical conditions
- Provides system health monitoring

### 7. Logging Module (`logging_module.py`)
- Centralized structured logging with time-stamping
- Logs events, warnings, errors, thread heartbeats
- Supports both console and file output
- Implements log rotation and cleanup
- Provides logging statistics and monitoring

### 8. Shutdown Module (`shutdown.py`)
- Coordinates clean stop across all threads using Event objects
- Implements phased shutdown: prepare → stop motors → stop sensors → cleanup
- Handles both graceful and emergency shutdown scenarios
- Monitors module shutdown timeouts
- Triggerable by error conditions or user command

### 9. Communication Module (`communication.py`)
- Provides wireless communication for external connectivity
- Handles remote monitoring and control commands
- Broadcasts telemetry data to connected clients
- Implements basic authentication and authorization
- Supports real-time status updates

## Thread Management & Communication

- **Threading**: Each module runs as an independent Python Thread
- **Communication**: Thread-safe Queue objects for inter-module messaging
- **Coordination**: Shared shutdown Event for synchronized termination
- **Safety**: Isolated module failures don't crash the entire system

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd robotour
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. For hardware integration, uncomment and install relevant dependencies in `requirements.txt`

## Usage

### Basic Operation

Run the main controller:
```bash
python main.py
```

The system will:
1. Initialize all modules
2. Start threads for each module
3. Enter the main control loop
4. Monitor system health
5. Handle navigation and safety

### Simulation Mode

By default, the system runs in simulation mode for testing without hardware:
- GPS module simulates location updates
- Vision module simulates obstacle detection
- Motors module simulates movement and odometry
- OSM module uses simulated map data

### Configuration

Key configuration parameters can be modified in each module:
- GPS update rates and accuracy thresholds
- Vision processing rates and safety distances  
- Motor acceleration limits and timeouts
- Communication ports and authentication
- Logging levels and file rotation

## Safety Features

The system implements multiple safety layers:

1. **Emergency Stop**: Vision module can trigger immediate motor stop
2. **Command Timeouts**: Motors stop if no commands received within timeout
3. **Watchdog Monitoring**: Automatic restart of failed modules
4. **Graceful Shutdown**: Coordinated shutdown preserves system state
5. **Module Isolation**: Thread failures don't affect other modules

## Development

### Adding New Modules

1. Create a new module file following the existing pattern
2. Implement the required `run(command_queue, status_queue, log_queue, shutdown_event)` method
3. Add the module to the main controller's initialization
4. Update the watchdog module to monitor the new module

### Testing

The system includes simulation modes for testing without hardware:
```bash
# Run with all modules in simulation mode
python main.py
```

### Logging

Logs are written to the `logs/` directory with automatic rotation:
- Structured JSON format for machine processing
- Human-readable console output
- Configurable log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Hardware Integration

To integrate with real hardware:

1. Install hardware-specific dependencies from `requirements.txt`
2. Disable simulation mode in relevant modules
3. Configure hardware interfaces (serial ports, GPIO pins, camera devices)
4. Test individual modules before full system integration

## Robotour Compliance

The system follows Robotour competition principles:
- **Safety by default**: Obstacle avoidance and safe-failure modes
- **Module isolation**: Independent module operation and safe restart
- **Clear telemetry**: Comprehensive logging and monitoring
- **Graceful shutdown**: Clean system termination
- **Scalability**: Easy addition of new modules without refactoring

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]

## Support

[Add support contact information here]
