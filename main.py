#!/usr/bin/env python3
"""
Main Controller for Robotic Vehicle
Initializes system modules, launches threads, monitors health, and orchestrates graceful shutdowns.
Implements main loop: collects status, makes decisions, issues commands.
"""

import threading
import time
import signal
import sys
from queue import Queue, Empty
from typing import Dict, List, Any

# Import all modules
from motors import MotorsModule
from gps import GPSModule
from vision import VisionModule
from osm import OSMModule
from watchdog import WatchdogModule
from logging_module import LoggingModule
from shutdown import ShutdownModule
from communication import CommunicationModule


class RobotController:
    """Main controller for the robotic vehicle system."""
    
    def __init__(self):
        """Initialize the robot controller with all modules."""
        # Create shutdown event for coordinated shutdown
        self.shutdown_event = threading.Event()
        
        # Create message queues for inter-module communication
        self.command_queue = Queue()
        self.status_queue = Queue()
        self.log_queue = Queue()
        
        # Initialize modules
        self.modules = {}
        self.threads = {}
        
        # System status
        self.system_status = {
            'motors': {'status': 'unknown', 'position': None, 'faults': []},
            'gps': {'status': 'unknown', 'location': None, 'accuracy': None},
            'vision': {'status': 'unknown', 'obstacles': [], 'path_clear': True},
            'osm': {'status': 'unknown', 'route': None, 'next_waypoint': None},
            'watchdog': {'status': 'unknown', 'thread_health': {}},
            'communication': {'status': 'unknown', 'connected': False},
            'logging': {'status': 'unknown', 'log_level': 'INFO'}
        }
        
        # Navigation state
        self.navigation_state = {
            'current_goal': None,
            'route_active': False,
            'emergency_stop': False,
            'manual_override': False
        }
        
        self.running = False
        
    def initialize_modules(self):
        """Initialize all system modules."""
        try:
            # Initialize logging first
            self.modules['logging'] = LoggingModule()
            
            # Initialize shutdown module
            self.modules['shutdown'] = ShutdownModule()
            
            # Initialize other modules
            self.modules['motors'] = MotorsModule()
            self.modules['gps'] = GPSModule()
            self.modules['vision'] = VisionModule()
            self.modules['osm'] = OSMModule()
            self.modules['watchdog'] = WatchdogModule()
            self.modules['communication'] = CommunicationModule()
            
            print("All modules initialized successfully")
            return True
            
        except Exception as e:
            print(f"Error initializing modules: {e}")
            return False
    
    def start_threads(self):
        """Start all module threads."""
        try:
            for module_name, module in self.modules.items():
                thread = threading.Thread(
                    target=module.run,
                    args=(self.command_queue, self.status_queue, self.log_queue, self.shutdown_event),
                    name=f"{module_name}_thread",
                    daemon=True
                )
                thread.start()
                self.threads[module_name] = thread
                print(f"Started {module_name} thread")
            
            return True
            
        except Exception as e:
            print(f"Error starting threads: {e}")
            return False
    
    def collect_status(self):
        """Collect status updates from all modules."""
        try:
            # Process all available status messages
            while True:
                try:
                    status_msg = self.status_queue.get_nowait()
                    module_name = status_msg.get('module')
                    if module_name in self.system_status:
                        self.system_status[module_name].update(status_msg.get('data', {}))
                except Empty:
                    break
                    
        except Exception as e:
            print(f"Error collecting status: {e}")
    
    def make_decisions(self):
        """Main decision-making logic based on current system status."""
        try:
            # Check for emergency conditions
            if self.system_status['vision']['obstacles']:
                if not self.navigation_state['emergency_stop']:
                    self.emergency_stop()
                    return
            
            # Check GPS and navigation status
            if (self.system_status['gps']['location'] and 
                self.navigation_state['route_active'] and 
                not self.navigation_state['emergency_stop']):
                
                # Get next navigation command from OSM
                if self.system_status['osm']['next_waypoint']:
                    self.send_navigation_command()
            
            # Monitor system health
            self.check_system_health()
            
        except Exception as e:
            print(f"Error in decision making: {e}")
    
    def send_navigation_command(self):
        """Send navigation commands to motors based on OSM guidance."""
        try:
            waypoint = self.system_status['osm']['next_waypoint']
            current_pos = self.system_status['gps']['location']
            
            if waypoint and current_pos:
                command = {
                    'module': 'motors',
                    'action': 'navigate_to',
                    'target': waypoint,
                    'current_position': current_pos,
                    'timestamp': time.time()
                }
                self.command_queue.put(command)
                
        except Exception as e:
            print(f"Error sending navigation command: {e}")
    
    def emergency_stop(self):
        """Execute emergency stop procedure."""
        try:
            self.navigation_state['emergency_stop'] = True
            
            # Send stop command to motors
            stop_command = {
                'module': 'motors',
                'action': 'emergency_stop',
                'timestamp': time.time()
            }
            self.command_queue.put(stop_command)
            
            # Log emergency stop
            log_msg = {
                'module': 'main',
                'level': 'WARNING',
                'message': 'Emergency stop activated due to obstacles',
                'timestamp': time.time()
            }
            self.log_queue.put(log_msg)
            
            print("EMERGENCY STOP ACTIVATED")
            
        except Exception as e:
            print(f"Error in emergency stop: {e}")
    
    def check_system_health(self):
        """Monitor overall system health."""
        try:
            # Check if any critical modules are not responding
            critical_modules = ['motors', 'gps', 'vision', 'watchdog']
            
            for module in critical_modules:
                if self.system_status[module]['status'] == 'error':
                    print(f"Critical module {module} in error state")
                    # Could trigger recovery procedures here
                    
        except Exception as e:
            print(f"Error checking system health: {e}")
    
    def signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown."""
        print(f"\nReceived signal {signum}. Initiating graceful shutdown...")
        self.shutdown()
    
    def shutdown(self):
        """Initiate graceful shutdown of all modules."""
        print("Initiating system shutdown...")
        self.running = False
        
        # Signal all modules to shutdown
        self.shutdown_event.set()
        
        # Wait for all threads to complete
        for module_name, thread in self.threads.items():
            print(f"Waiting for {module_name} thread to complete...")
            thread.join(timeout=5.0)
            if thread.is_alive():
                print(f"Warning: {module_name} thread did not shutdown cleanly")
        
        print("System shutdown complete")
    
    def run(self):
        """Main execution loop."""
        print("Starting Robotic Vehicle Controller...")
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Initialize and start system
        if not self.initialize_modules():
            print("Failed to initialize modules. Exiting.")
            return False
        
        if not self.start_threads():
            print("Failed to start threads. Exiting.")
            return False
        
        self.running = True
        print("System started successfully. Entering main loop...")
        
        # Main control loop
        try:
            while self.running and not self.shutdown_event.is_set():
                # Collect status from all modules
                self.collect_status()
                
                # Make decisions based on current state
                self.make_decisions()
                
                # Brief pause to prevent excessive CPU usage
                time.sleep(0.1)  # 10Hz main loop
                
        except KeyboardInterrupt:
            print("\nKeyboard interrupt received")
        except Exception as e:
            print(f"Error in main loop: {e}")
        finally:
            self.shutdown()
        
        return True


def main():
    """Entry point for the robotic vehicle controller."""
    controller = RobotController()
    success = controller.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
