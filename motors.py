#!/usr/bin/env python3
"""
Motors Module for Robotic Vehicle
Controls motor speed, direction, acceleration, braking.
Receives commands via thread-safe queue from main.
Emits status (position, faults) back to main.
"""

import threading
import time
import math
from queue import Empty
from typing import Dict, List, Tuple, Optional


class MotorsModule:
    """Motor control module for the robotic vehicle."""
    
    def __init__(self):
        """Initialize the motors module."""
        self.module_name = "motors"
        
        # Motor state
        self.left_motor_speed = 0.0  # -100 to 100 (percentage)
        self.right_motor_speed = 0.0  # -100 to 100 (percentage)
        self.max_speed = 100.0  # Maximum speed percentage
        self.acceleration_rate = 50.0  # Speed change per second
        
        # Position tracking (simulated odometry)
        self.position = {'x': 0.0, 'y': 0.0, 'heading': 0.0}  # heading in degrees
        self.wheel_diameter = 0.1  # meters
        self.wheel_base = 0.3  # meters (distance between wheels)
        
        # Motor status
        self.motor_faults = []
        self.emergency_stop_active = False
        self.motor_enabled = True
        self.last_command_time = 0
        self.command_timeout = 2.0  # seconds
        
        # Safety limits
        self.max_acceleration = 100.0  # max speed change per second
        self.min_turn_radius = 0.5  # minimum turning radius in meters
        
        # Thread control
        self.running = False
        self.status_update_rate = 10  # Hz
        
    def process_command(self, command: Dict):
        """Process incoming command from main controller."""
        try:
            action = command.get('action')
            timestamp = command.get('timestamp', time.time())
            
            # Update last command time
            self.last_command_time = timestamp
            
            if action == 'set_speed':
                left_speed = command.get('left_speed', 0.0)
                right_speed = command.get('right_speed', 0.0)
                self.set_motor_speeds(left_speed, right_speed)
                
            elif action == 'navigate_to':
                target = command.get('target')
                current_pos = command.get('current_position')
                if target and current_pos:
                    self.navigate_to_waypoint(target, current_pos)
                    
            elif action == 'emergency_stop':
                self.emergency_stop()
                
            elif action == 'enable_motors':
                self.enable_motors()
                
            elif action == 'disable_motors':
                self.disable_motors()
                
            elif action == 'reset_faults':
                self.reset_faults()
                
            else:
                self.add_fault(f"Unknown command action: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def set_motor_speeds(self, left_speed: float, right_speed: float):
        """Set motor speeds with safety checks and acceleration limiting."""
        try:
            if not self.motor_enabled or self.emergency_stop_active:
                return
            
            # Clamp speeds to valid range
            left_speed = max(-self.max_speed, min(self.max_speed, left_speed))
            right_speed = max(-self.max_speed, min(self.max_speed, right_speed))
            
            # Apply acceleration limiting
            left_speed = self.apply_acceleration_limit(self.left_motor_speed, left_speed)
            right_speed = self.apply_acceleration_limit(self.right_motor_speed, right_speed)
            
            # Update motor speeds
            self.left_motor_speed = left_speed
            self.right_motor_speed = right_speed
            
            # Update position based on new speeds (simplified odometry)
            self.update_position()
            
        except Exception as e:
            self.add_fault(f"Error setting motor speeds: {e}")
    
    def apply_acceleration_limit(self, current_speed: float, target_speed: float) -> float:
        """Apply acceleration limiting to prevent sudden speed changes."""
        speed_diff = target_speed - current_speed
        max_change = self.acceleration_rate / self.status_update_rate  # per update cycle
        
        if abs(speed_diff) <= max_change:
            return target_speed
        else:
            return current_speed + (max_change if speed_diff > 0 else -max_change)
    
    def navigate_to_waypoint(self, target: Dict, current_pos: Dict):
        """Calculate motor speeds to navigate to a target waypoint."""
        try:
            if not self.motor_enabled or self.emergency_stop_active:
                return
            
            # Extract coordinates
            target_lat = target.get('latitude', 0.0)
            target_lon = target.get('longitude', 0.0)
            current_lat = current_pos.get('latitude', 0.0)
            current_lon = current_pos.get('longitude', 0.0)
            
            # Calculate distance and bearing (simplified)
            dx = (target_lon - current_lon) * 111320 * math.cos(math.radians(current_lat))
            dy = (target_lat - current_lat) * 111320
            
            distance = math.sqrt(dx*dx + dy*dy)
            target_heading = math.degrees(math.atan2(dx, dy))
            
            # Calculate heading error
            heading_error = target_heading - self.position['heading']
            
            # Normalize heading error to [-180, 180]
            while heading_error > 180:
                heading_error -= 360
            while heading_error < -180:
                heading_error += 360
            
            # Simple proportional control
            if distance > 1.0:  # More than 1 meter away
                base_speed = min(50.0, distance * 10)  # Proportional to distance
                turn_rate = heading_error * 0.5  # Proportional to heading error
                
                # Calculate differential speeds
                left_speed = base_speed - turn_rate
                right_speed = base_speed + turn_rate
                
                self.set_motor_speeds(left_speed, right_speed)
            else:
                # Close to target, stop
                self.set_motor_speeds(0.0, 0.0)
                
        except Exception as e:
            self.add_fault(f"Error in navigation: {e}")
    
    def update_position(self):
        """Update robot position based on motor speeds (simplified odometry)."""
        try:
            dt = 1.0 / self.status_update_rate
            
            # Convert motor speeds to wheel velocities (simplified)
            left_velocity = (self.left_motor_speed / 100.0) * 2.0  # m/s
            right_velocity = (self.right_motor_speed / 100.0) * 2.0  # m/s
            
            # Calculate robot velocity and angular velocity
            linear_velocity = (left_velocity + right_velocity) / 2.0
            angular_velocity = (right_velocity - left_velocity) / self.wheel_base
            
            # Update position
            heading_rad = math.radians(self.position['heading'])
            self.position['x'] += linear_velocity * math.cos(heading_rad) * dt
            self.position['y'] += linear_velocity * math.sin(heading_rad) * dt
            self.position['heading'] += math.degrees(angular_velocity * dt)
            
            # Normalize heading
            while self.position['heading'] > 180:
                self.position['heading'] -= 360
            while self.position['heading'] < -180:
                self.position['heading'] += 360
                
        except Exception as e:
            self.add_fault(f"Error updating position: {e}")
    
    def emergency_stop(self):
        """Execute emergency stop procedure."""
        self.emergency_stop_active = True
        self.left_motor_speed = 0.0
        self.right_motor_speed = 0.0
        self.add_fault("Emergency stop activated")
    
    def enable_motors(self):
        """Enable motor control."""
        self.motor_enabled = True
        self.emergency_stop_active = False
    
    def disable_motors(self):
        """Disable motor control."""
        self.motor_enabled = False
        self.left_motor_speed = 0.0
        self.right_motor_speed = 0.0
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.motor_faults.append(fault)
        
        # Keep only recent faults (last 100)
        if len(self.motor_faults) > 100:
            self.motor_faults = self.motor_faults[-100:]
    
    def reset_faults(self):
        """Clear all motor faults."""
        self.motor_faults.clear()
    
    def check_command_timeout(self):
        """Check if commands have timed out and stop motors if needed."""
        if (time.time() - self.last_command_time) > self.command_timeout:
            if self.left_motor_speed != 0 or self.right_motor_speed != 0:
                self.set_motor_speeds(0.0, 0.0)
                self.add_fault("Command timeout - motors stopped")
    
    def get_status(self) -> Dict:
        """Get current motor status."""
        return {
            'module': self.module_name,
            'data': {
                'status': 'error' if self.motor_faults else ('disabled' if not self.motor_enabled else 'running'),
                'left_motor_speed': self.left_motor_speed,
                'right_motor_speed': self.right_motor_speed,
                'position': self.position.copy(),
                'faults': self.motor_faults.copy(),
                'emergency_stop': self.emergency_stop_active,
                'enabled': self.motor_enabled,
                'timestamp': time.time()
            }
        }
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        last_status_time = 0
        status_interval = 1.0 / self.status_update_rate
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'Motors module started',
            'timestamp': time.time()
        })
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Check for command timeout
                self.check_command_timeout()
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep to prevent excessive CPU usage
                time.sleep(0.01)
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'Motors module error: {e}',
                'timestamp': time.time()
            })
        finally:
            # Ensure motors are stopped on shutdown
            self.left_motor_speed = 0.0
            self.right_motor_speed = 0.0
            self.running = False
            
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'Motors module stopped',
                'timestamp': time.time()
            })
