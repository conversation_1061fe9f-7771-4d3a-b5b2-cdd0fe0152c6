@echo off
echo ===================================
echo INSTALACIA KVALIFIKACNEHO PROGRAMU
echo ===================================
echo.

echo Kontrolujem Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: Python nie je nainstalovany alebo nie je v PATH!
    echo Nainštalujte Python 3.8+ z https://python.org
    pause
    exit /b 1
)

echo Python najdeny.
echo.

echo Instalujem zavislosti...
pip install -r kvalifikacia_requirements.txt

if errorlevel 1 (
    echo CHYBA: Nepodarilo sa nainštalovat zavislosti!
    pause
    exit /b 1
)

echo.
echo Zavislosti uspesne nainstalovane.
echo.

echo Spustam test setup...
python test_setup.py

echo.
echo ===================================
echo INSTALACIA DOKONCENA
echo ===================================
echo.
echo Pre spustenie kvalifikacneho programu pouzite:
echo python kvalifikacia_demo.py
echo.
pause
