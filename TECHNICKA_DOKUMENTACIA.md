# Technicka Dokumentacia - Kvalifikacny Program

## Architektura Systemu

### Komponenty
```
kvalifikacia_demo.py
├── QRCodeDetector          # QR kod detekcia
├── KvalifikacnyProgram     # Hlavna logika
├── motordriver/            # Motor control kniznica
│   ├── robot_driver.py
│   └── robot_driver_demo.py
└── camera/camera/          # Camera kniznica
    ├── camera_module.py
    └── demo.py
```

### Datovy Tok
```
QR Kod → Camera → QRCodeDetector → KvalifikacnyProgram
                                        ↓
Obstacle Detection ← Camera ← CameraModule
                                        ↓
Motor Commands → RobotDriver → Serial Port → Robot
```

## Implementacne Detaily

### QR Kod Detekcia
```python
class QRCodeDetector:
    def __init__(self):
        self.qr_detector = cv2.QRCodeDetector()
    
    def detect_qr_code(self, frame):
        data, bbox, _ = self.qr_detector.detectAndDecode(frame)
        return data if data else None
```

**Poznamky:**
- Pouziva OpenCV QRCodeDetector
- Vracia obsah QR kodu alebo None
- <PERSON>gu<PERSON> s lubovolnymi QR kodmi

### Motor Control
```python
# Inicializacia
self.robot = RobotDriver(port, baudrate=9600)
self.robot.start()

# Pohyb
self.robot.move_forward(speed_percent)
self.robot.stop_motors()
self.robot.turn_right(speed_percent)
```

**Protokol:**
- Seriova komunikacia 9600 baud
- Automaticke periodické posielanie prikazov (200ms)
- Bezpecnostny timeout 300ms

### Camera Processing
```python
# Inicializacia
self.camera = CameraModule(camera_index=1)
self.camera.initialize()

# Spracovanie
frame = self.camera.capture_frame()
result = self.camera.process_frame(frame)
```

**CameraResult struktura:**
```python
@dataclass
class CameraResult:
    direction: Direction        # FORWARD, LEFT, RIGHT, STOP
    confidence: float          # 0.0 - 1.0
    obstacle_detected: bool    # True/False
    path_center_offset: float  # -1.0 (lavo) to 1.0 (pravo)
```

## Stavovy Automat

### Stavy Programu
```
INIT → WAIT_QR → COUNTDOWN → DRIVING → [OBSTACLE] → BYPASS → DRIVING → END
```

### Prechody Stavov
1. **INIT → WAIT_QR**: Po inicializacii komponentov
2. **WAIT_QR → COUNTDOWN**: Po detekcii QR kodu
3. **COUNTDOWN → DRIVING**: Po 3s odpocitavani
4. **DRIVING → OBSTACLE**: Pri detekcii prekazky
5. **OBSTACLE → DRIVING**: Ak prekazka zmizne do 10s
6. **OBSTACLE → BYPASS**: Ak prekazka nezmizne po 10s
7. **BYPASS → DRIVING**: Po dokonceni obchadzacieho manevru
8. **DRIVING → END**: Po prejdeni 10m alebo manualne ukoncenie

## Algoritmy

### Odhad Vzdialenosti
```python
def estimate_distance(self, speed_percent, time_seconds):
    # Jednoduchy linearny model
    estimated_speed_ms = (speed_percent / 100.0) * 0.5  # 10% = 0.5 m/s
    return estimated_speed_ms * time_seconds
```

**Kalibrácia:**
- 10% rychlost ≈ 0.5 m/s (empiricky)
- Mozne upravit podla skutocnej rychlosti robota

### Detekcia Prekazky
```python
# V camera_module.py
def detect_obstacle(self, frame):
    # ROI v spodnej casti snimky
    roi_y_start = height // 2
    roi = frame[roi_y_start:, :]
    
    # Porovnanie s referencnym snimkom
    if self.reference_frame is not None:
        diff = cv2.absdiff(roi_gray, ref_roi_gray)
        change_ratio = np.sum(diff > threshold) / total_pixels
        return change_ratio > self.obstacle_threshold
```

### Obchadzaci Manever
```python
def perform_bypass_maneuver(self):
    # Jednoduchy pravostranny obchod
    self.robot.turn_right(30)    # Otoc doprava
    time.sleep(2)
    self.robot.move_forward(10)  # Jdi dopredu
    time.sleep(3)
    self.robot.turn_left(30)     # Otoc dolava
    time.sleep(2)
    self.robot.move_forward(10)  # Jdi dopredu
    time.sleep(3)
    self.robot.turn_left(30)     # Navrat na povodny smer
    time.sleep(2)
```

## Konfiguracne Parametre

### Hlavne Konstanty
```python
class KvalifikacnyProgram:
    def __init__(self):
        self.target_distance = 10.0      # Cielova vzdialenost [m]
        self.speed = 10                  # Rychlost [%]
        self.OBSTACLE_WAIT_TIME = 10.0   # Cakanie pri prekazke [s]
        self.COUNTDOWN_TIME = 3          # Odpocitavanie [s]
        self.OBSTACLE_DISTANCE_THRESHOLD = 1.0  # Prah detekcie [m]
```

### Camera Parametre
```python
class CameraModule:
    def __init__(self):
        self.frame_width = 640
        self.frame_height = 480
        self.obstacle_wait_duration = 10.0
        self.obstacle_threshold = 0.15  # 15% zmena pre detekciu
```

## Logovanie a Diagnostika

### Log Levels
- **INFO**: Normalne operacie (start, stop, detekcie)
- **WARNING**: Varovania (prekazky, nízka spolahlivos)
- **ERROR**: Chyby (inicializacia, komunikacia)

### Log Format
```
2025-01-29 10:30:15,123 - kvalifikacia_demo - INFO - QR kod nacitany: START
2025-01-29 10:30:18,456 - kvalifikacia_demo - WARNING - Prekazka detekovana!
```

### Diagnosticke Nastroje
- `test_setup.py` - Test vsetkych komponentov
- `kvalifikacia.log` - Podrobne logy
- Graficke zobrazenie stavu v realnom case

## Bezpecnost a Error Handling

### Bezpecnostne Opatrenia
1. **Automaticke zastavenie** pri detekcii prekazky
2. **Graceful shutdown** pri chybach
3. **Timeout protection** pre vsetky operacie
4. **Resource cleanup** pri ukonceni

### Error Recovery
```python
try:
    # Kritická operacia
    result = self.robot.move_forward(speed)
except Exception as e:
    logger.error(f"Chyba: {e}")
    self.robot.stop_motors()  # Bezpecne zastavenie
    self.cleanup()            # Upratanie
```

## Rozšírenia a Modifikacie

### Mozne Vylepsenia
1. **Presnejsi odhad vzdialenosti** - enkodery, IMU
2. **Pokrocila detekcia prekazky** - LIDAR, ultrazvuk
3. **Inteligentnejsi obchod** - path planning
4. **Kalibrácia parametrov** - GUI nastavenia
5. **Telemetria** - vzdialene monitorovanie

### Modifikacia Parametrov
```python
# V kvalifikacia_demo.py, metoda __init__
self.target_distance = 15.0    # Zmena cielovej vzdialenosti
self.speed = 15               # Zmena rychlosti
self.OBSTACLE_WAIT_TIME = 5.0 # Kratšie cakanie
```

### Pridanie Novych Senzorov
```python
class KvalifikacnyProgram:
    def __init__(self):
        # Pridanie noveho senzora
        self.ultrasonic = UltrasonicSensor(pin=18)
    
    def enhanced_obstacle_detection(self):
        # Kombinacia camera + ultrazvuk
        camera_obstacle = self.camera_obstacle_detected
        ultrasonic_distance = self.ultrasonic.get_distance()
        return camera_obstacle or ultrasonic_distance < 0.5
```

## Testovanie

### Unit Testy
```python
def test_qr_detection():
    detector = QRCodeDetector()
    test_frame = cv2.imread('test_qr.png')
    result = detector.detect_qr_code(test_frame)
    assert result == "START"

def test_distance_estimation():
    program = KvalifikacnyProgram()
    distance = program.estimate_distance(10, 2.0)  # 10%, 2s
    assert distance == 1.0  # 0.5 m/s * 2s = 1m
```

### Integracne Testy
```python
def test_full_sequence():
    program = KvalifikacnyProgram()
    program.initialize_components()
    # Simulacia QR kodu
    # Simulacia jazdy
    # Overenie vysledku
```

## Performance

### Optimalizacie
- Camera FPS: 30 FPS (mozne znizit na 15 FPS)
- Processing interval: 50ms
- Motor command interval: 200ms
- Log flush interval: 1s

### Resource Usage
- RAM: ~50MB (OpenCV + camera buffer)
- CPU: ~10% (spracovanie obrazu)
- Disk: ~1MB/hodina (logy)

---

**Poznamka**: Tato dokumentacia je urcena pre vyvojarov, ktori chcu rozumiet alebo modifikovat kod programu.
