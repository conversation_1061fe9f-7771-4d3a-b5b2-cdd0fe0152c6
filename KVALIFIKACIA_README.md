# Kvalifikacny Program pre Robotour

Tento program implementuje kvalifikacnu jazdu pre sutaz Robotour. Program integruje motordriver a camera kniznice pre autonomnu jazdu robota.

## Popis Kvalifikacie

Kvalifikacia je jednoducha autonomna jazda:
1. <PERSON> po odstartovani prejde usek cca 10m
2. Pocas jazdy detekuje prekazky
3. Pri detekcii prekazky sa zastavi na 10s
4. Ak prekazka zmizne, pokracuje v jazde
5. Ak prekazka nezmizne, zacne obchadzaci manever

## Funkcionalita Programu

### Sekvencia Programu
1. **Cakanie na QR kod** - Program caka na nacitanie QR kodu z kamery pre spustenie
2. **Signalizacia** - Po nacitani QR kodu pipne beeper
3. **Odpocitavanie** - 3 sekundy odpocitavania do startu s pipnutim kazdu sekundu
4. **Start** - Dlhy pip a zacatie jazdy rychlostou 10% dopredu
5. **Jazda** - Robot ide dopredu a monitoruje prekazky pomocou camera modulu
6. **Detekcia prekazky** - Pri detekcii prekazky vo vzdialenosti 0-1m sa zastavi
7. **Cakanie** - 10 sekund cakania na zmiznutie prekazky
8. **Rozhodnutie** - Ak prekazka zmizne, pokracuje; ak nie, obchadzaci manever rychlostou 10%
9. **Ukoncenie** - Po 10m sa robot zastavi a kolesa sa prestanu tocit

### Graficke Zobrazenie
- Zobrazenie kamery v realnom case
- Informacie o stave robota (rychlost, vzdialenost, detekcia prekazky)
- Progress bar pre prejdenu vzdialenost
- Farebne oznacenie stavu (zelena = OK, cervena = prekazka)

## Instalacia a Spustenie

### Poziadavky
- Python 3.8+
- USB kamera (index 1)
- Seriovy port pre motordriver (predvolene COM4)
- Windows OS (pre beeper funkcionalitu)

### Instalacia Zavislosti
```bash
pip install -r kvalifikacia_requirements.txt
```

### Spustenie
```bash
python kvalifikacia_demo.py
```

## Konfiguracia

### Porty a Zariadenia
Pri spusteni program umozni nastavit:
- **Motor port**: Seriovy port pre motordriver (predvolene COM4)
- **Camera index**: Index USB kamery (predvolene 1)

### Konstanty v Kode
```python
# V triede KvalifikacnyProgram
self.target_distance = 10.0      # Cielova vzdialenost v metroch
self.speed = 10                  # Rychlost v percentach
self.OBSTACLE_WAIT_TIME = 10.0   # Cas cakania pri prekazke v sekundach
self.COUNTDOWN_TIME = 3          # Cas odpocitavania v sekundach
```

## Struktura Kodu

### Hlavne Triedy

#### `QRCodeDetector`
- Detekcia QR kodov pomocou OpenCV
- Metoda `detect_qr_code()` vracia obsah QR kodu

#### `KvalifikacnyProgram`
- Hlavna trieda programu
- Integruje motordriver a camera kniznice
- Implementuje celkovu logiku kvalifikacie

### Hlavne Metody

#### `initialize_components()`
- Inicializacia motordriver a camera modulov
- Nastavenie zakladnych parametrov

#### `wait_for_qr_code()`
- Cakanie na nacitanie QR kodu z kamery
- Zobrazenie kamery v realnom case

#### `countdown()`
- 3-sekundove odpocitavanie s beeper signalizaciou

#### `drive_sequence()`
- Hlavna sekvencia jazdy
- Monitorovanie prekazok a riadenie robota

#### `handle_obstacle()`
- Spracovanie detekcie prekazky
- Implementacia 10-sekundoveho cakania

#### `perform_bypass_maneuver()`
- Jednoduchy obchadzaci manever (doprava-dopredu-dolava-dopredu-dolava)

## Logovanie

Program vytvara podrobne logy v subore `kvalifikacia.log`:
- Vsetky akcie a stavy robota
- Detekcie prekazok a QR kodov
- Chyby a varovania
- Casove znacky pre vsetky udalosti

## Ovladanie

### Klavesove Skratky
- **'q'** - Ukoncenie programu v lubovolnom stave
- **Ctrl+C** - Nouzove ukoncenie programu
- **Enter** - Pokracovanie pri spusteni

### Beeper Signaly
- **Kratky pip (200ms)** - QR kod nacitany
- **Pip kazdu sekundu** - Odpocitavanie
- **Dlhy pip (1000ms)** - Start jazdy

## Bezpecnost

### Automaticke Zastavenie
- Robot sa automaticky zastavi pri detekcii prekazky
- Vsetky motory sa zastavia pri ukonceni programu
- Bezpecne ukoncenie pri chybach

### Monitorovanie
- Kontinualne monitorovanie stavu kamery
- Logovanie vsetkych kritickych operacii
- Graceful shutdown pri chybach

## Riešenie Problemov

### Kamera sa Nenajde
```
Chyba: Nepodarilo sa inicializovat kameru
```
**Riesenie:**
- Skontrolujte pripojenie USB kamery
- Skuste iny camera index (0, 1, 2...)
- Overte ze kamera nie je pouzivana inym programom

### Motor Port Nedostupny
```
Chyba: Nepodarilo sa inicializovat motordriver
```
**Riesenie:**
- Skontrolujte pripojenie serioveho portu
- Overte spravny nazov portu (COM3, COM4...)
- Skontrolujte ze port nie je pouzivany inym programom

### QR Kod sa Necita
**Riesenie:**
- Pouzite kontrastny QR kod na bielom pozadi
- Umiestnite QR kod do centra kamery
- Zabezpecte dobre osvetlenie
- QR kod by mal byt dostatocne velky a ostry

### Beeper Nefunguje
```
Varovanie: Beeper nedostupny
```
**Riesenie:**
- Program pokracuje bez beepera
- Na konzole sa zobrazi "BEEP!" namiesto zvuku
- Funkcionalita je zachovana

## Technické Detaily

### Integrovane Kniznice
- **motordriver** - Ovladanie motorov cez seriovy port
- **camera** - Spracovanie kamery a detekcia prekazok
- **OpenCV** - QR kod detekcia a zobrazenie kamery

### Odhad Vzdialenosti
Program pouziva jednoduchy odhad prejdenej vzdialenosti:
- 10% rychlost ≈ 0.5 m/s
- Vzdialenost = rychlost × cas

### Detekcia Prekazok
Pouziva sa existujuca funkcionalita z camera modulu:
- Detekcia zmien v obraze
- ROI (Region of Interest) v spodnej casti snimky
- Prah detekcie nastaveny na 1 meter

## Autor a Verzia

- **Autor**: Robotour Team
- **Verzia**: 1.0
- **Datum**: 2025-01-29
- **Licencia**: Pre potreby sutaze Robotour

## Podpora

Pre otazky a problemy kontaktujte tym Robotour alebo vytvorte issue v repository.

---

**Poznamka**: Program je optimalizovany pre Windows. Pre Linux/Mac moze byt potrebne upravit beeper funkcionalitu a cesty k portom.
