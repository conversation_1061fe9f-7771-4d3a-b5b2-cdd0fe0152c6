#!/usr/bin/env python3
"""
Vision Module for Robotic Vehicle
Camera input, obstacle detection, path validation.
Ensures pedestrian safety and collision avoidance.
"""

import threading
import time
import random
import math
from queue import Empty
from typing import Dict, List, Tuple, Optional


class VisionModule:
    """Vision module for camera input and obstacle detection."""
    
    def __init__(self):
        """Initialize the vision module."""
        self.module_name = "vision"
        
        # Camera configuration
        self.camera_enabled = False
        self.camera_resolution = (640, 480)
        self.camera_fps = 30
        self.field_of_view = 60  # degrees
        
        # Detection parameters
        self.detection_range = 10.0  # meters
        self.safety_distance = 2.0  # minimum safe distance to obstacles
        self.pedestrian_safety_distance = 3.0  # extra safety for pedestrians
        
        # Current detections
        self.obstacles = []
        self.pedestrians = []
        self.path_clear = True
        self.emergency_stop_required = False
        
        # Detection confidence thresholds
        self.obstacle_confidence_threshold = 0.7
        self.pedestrian_confidence_threshold = 0.8
        
        # Simulation mode for testing
        self.simulation_mode = True
        self.sim_obstacle_probability = 0.1  # 10% chance per frame
        self.sim_pedestrian_probability = 0.05  # 5% chance per frame
        
        # Vision status
        self.vision_faults = []
        self.last_frame_time = 0
        self.frame_count = 0
        self.processing_fps = 0
        
        # Thread control
        self.running = False
        self.processing_rate = 10  # Hz (vision processing rate)
        
    def initialize_camera(self) -> bool:
        """Initialize camera hardware."""
        try:
            if self.simulation_mode:
                # Simulate camera initialization
                self.camera_enabled = True
                return True
            else:
                # Real camera initialization would go here
                # This would involve opening camera device, setting resolution, etc.
                # For example, using OpenCV:
                # import cv2
                # self.camera = cv2.VideoCapture(0)
                # self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.camera_resolution[0])
                # self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.camera_resolution[1])
                self.camera_enabled = True
                return True
                
        except Exception as e:
            self.add_fault(f"Camera initialization failed: {e}")
            return False
    
    def capture_frame(self) -> Optional[Dict]:
        """Capture a frame from the camera."""
        try:
            if not self.camera_enabled:
                return None
            
            if self.simulation_mode:
                return self.simulate_frame_capture()
            else:
                # Real frame capture would go here
                # ret, frame = self.camera.read()
                # if ret:
                #     return {'frame': frame, 'timestamp': time.time()}
                return None
                
        except Exception as e:
            self.add_fault(f"Error capturing frame: {e}")
            return None
    
    def simulate_frame_capture(self) -> Dict:
        """Simulate frame capture for testing."""
        current_time = time.time()
        
        # Simulate frame data (placeholder)
        frame_data = {
            'timestamp': current_time,
            'frame_id': self.frame_count,
            'resolution': self.camera_resolution,
            'simulated': True
        }
        
        self.frame_count += 1
        self.last_frame_time = current_time
        
        return frame_data
    
    def detect_obstacles(self, frame_data: Dict) -> List[Dict]:
        """Detect obstacles in the frame."""
        try:
            if self.simulation_mode:
                return self.simulate_obstacle_detection()
            else:
                # Real obstacle detection would go here
                # This would involve computer vision algorithms like:
                # - YOLO object detection
                # - Depth estimation
                # - Semantic segmentation
                return []
                
        except Exception as e:
            self.add_fault(f"Error in obstacle detection: {e}")
            return []
    
    def simulate_obstacle_detection(self) -> List[Dict]:
        """Simulate obstacle detection for testing."""
        obstacles = []
        
        # Randomly generate obstacles
        if random.random() < self.sim_obstacle_probability:
            num_obstacles = random.randint(1, 3)
            
            for i in range(num_obstacles):
                obstacle = {
                    'id': f"obstacle_{self.frame_count}_{i}",
                    'type': random.choice(['static', 'vehicle', 'unknown']),
                    'distance': random.uniform(1.0, self.detection_range),
                    'angle': random.uniform(-30, 30),  # degrees from center
                    'width': random.uniform(0.5, 2.0),  # meters
                    'height': random.uniform(0.5, 2.0),  # meters
                    'confidence': random.uniform(0.7, 0.95),
                    'velocity': random.uniform(-2.0, 2.0),  # m/s (negative = approaching)
                    'timestamp': time.time()
                }
                obstacles.append(obstacle)
        
        return obstacles
    
    def detect_pedestrians(self, frame_data: Dict) -> List[Dict]:
        """Detect pedestrians in the frame."""
        try:
            if self.simulation_mode:
                return self.simulate_pedestrian_detection()
            else:
                # Real pedestrian detection would go here
                # This would involve specialized person detection models
                return []
                
        except Exception as e:
            self.add_fault(f"Error in pedestrian detection: {e}")
            return []
    
    def simulate_pedestrian_detection(self) -> List[Dict]:
        """Simulate pedestrian detection for testing."""
        pedestrians = []
        
        # Randomly generate pedestrians
        if random.random() < self.sim_pedestrian_probability:
            num_pedestrians = random.randint(1, 2)
            
            for i in range(num_pedestrians):
                pedestrian = {
                    'id': f"person_{self.frame_count}_{i}",
                    'distance': random.uniform(2.0, self.detection_range),
                    'angle': random.uniform(-45, 45),  # degrees from center
                    'confidence': random.uniform(0.8, 0.98),
                    'velocity': random.uniform(-1.5, 1.5),  # m/s
                    'pose': random.choice(['standing', 'walking', 'running']),
                    'timestamp': time.time()
                }
                pedestrians.append(pedestrian)
        
        return pedestrians
    
    def analyze_path_safety(self, obstacles: List[Dict], pedestrians: List[Dict]) -> Dict:
        """Analyze path safety based on detected objects."""
        try:
            path_analysis = {
                'path_clear': True,
                'emergency_stop_required': False,
                'recommended_action': 'continue',
                'closest_obstacle_distance': float('inf'),
                'closest_pedestrian_distance': float('inf')
            }
            
            # Check obstacles
            for obstacle in obstacles:
                distance = obstacle['distance']
                angle = abs(obstacle['angle'])
                
                # Update closest obstacle
                if distance < path_analysis['closest_obstacle_distance']:
                    path_analysis['closest_obstacle_distance'] = distance
                
                # Check if obstacle is in path (within ±15 degrees)
                if angle <= 15:
                    if distance < self.safety_distance:
                        path_analysis['path_clear'] = False
                        path_analysis['recommended_action'] = 'stop'
                        
                        if distance < self.safety_distance * 0.5:
                            path_analysis['emergency_stop_required'] = True
            
            # Check pedestrians (higher priority)
            for pedestrian in pedestrians:
                distance = pedestrian['distance']
                angle = abs(pedestrian['angle'])
                
                # Update closest pedestrian
                if distance < path_analysis['closest_pedestrian_distance']:
                    path_analysis['closest_pedestrian_distance'] = distance
                
                # Check if pedestrian is in or near path (within ±20 degrees)
                if angle <= 20:
                    if distance < self.pedestrian_safety_distance:
                        path_analysis['path_clear'] = False
                        path_analysis['recommended_action'] = 'stop'
                        path_analysis['emergency_stop_required'] = True
            
            return path_analysis
            
        except Exception as e:
            self.add_fault(f"Error analyzing path safety: {e}")
            return {
                'path_clear': False,
                'emergency_stop_required': True,
                'recommended_action': 'stop',
                'closest_obstacle_distance': 0.0,
                'closest_pedestrian_distance': 0.0
            }
    
    def update_processing_fps(self):
        """Update processing FPS calculation."""
        current_time = time.time()
        if hasattr(self, 'fps_start_time'):
            elapsed = current_time - self.fps_start_time
            if elapsed >= 1.0:  # Update every second
                self.processing_fps = self.fps_frame_count / elapsed
                self.fps_start_time = current_time
                self.fps_frame_count = 0
        else:
            self.fps_start_time = current_time
            self.fps_frame_count = 0
        
        self.fps_frame_count += 1
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.vision_faults.append(fault)
        
        # Keep only recent faults (last 50)
        if len(self.vision_faults) > 50:
            self.vision_faults = self.vision_faults[-50:]
    
    def get_status(self) -> Dict:
        """Get current vision status."""
        status = 'running' if self.camera_enabled else 'camera_disabled'
        if self.vision_faults:
            status = 'error'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'camera_enabled': self.camera_enabled,
                'obstacles': self.obstacles.copy(),
                'pedestrians': self.pedestrians.copy(),
                'path_clear': self.path_clear,
                'emergency_stop_required': self.emergency_stop_required,
                'processing_fps': self.processing_fps,
                'frame_count': self.frame_count,
                'faults': self.vision_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'enable_camera':
                self.initialize_camera()
            elif action == 'disable_camera':
                self.camera_enabled = False
            elif action == 'reset_faults':
                self.vision_faults.clear()
            elif action == 'set_simulation_mode':
                self.simulation_mode = command.get('enabled', True)
            elif action == 'set_safety_distance':
                self.safety_distance = command.get('distance', self.safety_distance)
            else:
                self.add_fault(f"Unknown vision command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'Vision module started',
            'timestamp': time.time()
        })
        
        # Initialize camera
        if not self.initialize_camera():
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': 'Failed to initialize camera',
                'timestamp': time.time()
            })
        
        last_status_time = 0
        status_interval = 1.0  # Send status every second
        processing_interval = 1.0 / self.processing_rate
        last_processing_time = 0
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Process vision data
                if current_time - last_processing_time >= processing_interval:
                    if self.camera_enabled:
                        # Capture frame
                        frame_data = self.capture_frame()
                        
                        if frame_data:
                            # Detect obstacles and pedestrians
                            self.obstacles = self.detect_obstacles(frame_data)
                            self.pedestrians = self.detect_pedestrians(frame_data)
                            
                            # Analyze path safety
                            path_analysis = self.analyze_path_safety(self.obstacles, self.pedestrians)
                            self.path_clear = path_analysis['path_clear']
                            self.emergency_stop_required = path_analysis['emergency_stop_required']
                            
                            # Update FPS calculation
                            self.update_processing_fps()
                    
                    last_processing_time = current_time
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.05)  # 20Hz loop
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'Vision module error: {e}',
                'timestamp': time.time()
            })
        finally:
            self.running = False
            if hasattr(self, 'camera') and not self.simulation_mode:
                # Release camera resources
                # self.camera.release()
                pass
            
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'Vision module stopped',
                'timestamp': time.time()
            })
