# Nastavenia Detekcie Prekazok

## Vzdialenost vs Citlivost

Program automaticky prepocitava citlivost detekcie na zaklade pozadovanej vzdialenosti.

### Tabulka Prepoctu

| Vzdialenost | Citlivost | Popis | Pouzitie |
|-------------|-----------|-------|----------|
| 5cm | 38.25% | <PERSON><PERSON><PERSON> blizko | Tesne pred robotom |
| 10cm | 36.5% | Bliz<PERSON> | Bezpecna vzdialenost |
| 15cm | 34.75% | St<PERSON>ne blizko | Odporucane pre testovanie |
| 20cm | 33% | Stredne | Normalna prevádzka |
| 30cm | 29.5% | Stredne daleko | Konzervatívne nastavenie |
| 50cm | 22.5% | Daleko | Default nastavenie |
| 70cm | 15.5% | <PERSON><PERSON><PERSON><PERSON> | Pre rychlu jazdu |
| 100cm | 5% | Maximum | Minimalna citlivost |

### Formula Prepoctu

```python
citlivost_percent = max(5.0, 40.0 - (vzdialenost_cm * 0.35))
```

**Vysvetlenie:**
- Zakladna citlivost: 40%
- Znizenie: 0.35% za kazdy cm
- Minimum: 5% (pre vzdialenosti nad 100cm)

## Odporucane Nastavenia

### Pre Kvalifikaciu
- **Vzdialenost**: 10-20cm
- **Citlivost**: 33-36.5%
- **Popis**: Bezpecna vzdialenost s rychlou reakciou

### Pre Testovanie
- **Vzdialenost**: 50cm (default)
- **Citlivost**: 22.5%
- **Popis**: Pohodlne testovanie bez castych zastaveni

### Pre Rychlu Jazdu
- **Vzdialenost**: 70-100cm
- **Citlivost**: 5-15.5%
- **Popis**: Minimalne zastavenia, len pri velkych prekazkach

## Kalibrácia

### Ako Nastavit Spravnu Vzdialenost

1. **Spustite program** s default 50cm
2. **Testujte detekciu** - postavte sa pred robot
3. **Zmerajte vzdialenost** kedy sa robot zastavi
4. **Upravte nastavenie**:
   - Ak sa zastavi prilis daleko → zvyste vzdialenost (napr. 70cm)
   - Ak sa zastavi prilis blizko → znizste vzdialenost (napr. 20cm)

### Testovaci Postup

```bash
# Test s roznou vzdialenostou
python kvalifikacia_demo.py
# Vzdialenost detekcie prekazky v cm: 10

# Sledujte v GUI:
# "Detekcia na: 10cm (36.5%)"
```

### Faktory Ovplyvnujuce Detekciu

#### Osvetlenie
- **Jasne svetlo**: Mozno potrebovat nizsiu citlivost
- **Tmave prostredie**: Mozno potrebovat vyssiu citlivost

#### Typ Prekazky
- **Tmave objekty**: Lepsie detekovane
- **Svetle objekty**: Horsie detekovane
- **Kontrastne objekty**: Najlepsie detekovane

#### Rychlost Robota
- **Pomala jazda (10%)**: Mozno pouzit nizsiu vzdialenost
- **Rychla jazda**: Potrebna vyssia vzdialenost

## Riešenie Problemov

### Robot sa Nezastavi
**Mozne priciny:**
- Vzdialenost prilis vysoka (napr. 100cm)
- Prekazka prilis svetla
- Slabe osvetlenie

**Riesenie:**
- Znizste vzdialenost na 10-20cm
- Pouzite tmavšie objekty pre test
- Zlepsie osvetlenie

### Robot sa Zastavi Prilis Casto
**Mozne priciny:**
- Vzdialenost prilis nizka (napr. 5cm)
- Prilis citlive na zmeny osvetlenia
- Pohybujuce sa tiene

**Riesenie:**
- Zvyste vzdialenost na 50-70cm
- Stabilizujte osvetlenie
- Odstranite pohybujuce sa objekty

### Falošne Alarmy
**Mozne priciny:**
- Zmeny osvetlenia
- Tiene z okien
- Reflexie

**Riesenie:**
- Pouzite konstantne osvetlenie
- Zatvorte zavesy/zalúzie
- Odstranite reflexne povrchy

---

**Poznamka**: Tieto nastavenia su empiricky testovane. Mozno ich bude potrebne doladit podla konkretnych podmienok.
