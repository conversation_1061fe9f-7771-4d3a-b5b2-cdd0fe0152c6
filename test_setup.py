#!/usr/bin/env python3
"""
Test Setup Script pre Kvalifikacny Program
Testuje dostupnost seriových portov a kamier.

Autor: Robotour Team
Datum: 2025-01-29
"""

import sys
import os
import cv2
import serial.tools.list_ports
import logging

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_serial_ports():
    """Test dostupnych seriovych portov"""
    print("=== TEST SERIOVYCH PORTOV ===")
    
    ports = serial.tools.list_ports.comports()
    if not ports:
        print("CHYBA: Ziadne seriove porty nenajdene!")
        return []
    
    available_ports = []
    for port in ports:
        print(f"Najdeny port: {port.device} - {port.description}")
        available_ports.append(port.device)
    
    return available_ports

def test_cameras():
    """Test dostupnych kamier"""
    print("\n=== TEST KAMIER ===")
    
    available_cameras = []
    
    # Test kamier s indexmi 0-3
    for i in range(4):
        try:
            print(f"Testujem kameru s indexom {i}...")
            cap = cv2.VideoCapture(i)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"  ✓ Kamera {i}: OK - Rozlisenie {width}x{height}")
                    available_cameras.append(i)
                else:
                    print(f"  ✗ Kamera {i}: Nedostupna (nemoze citat snimky)")
            else:
                print(f"  ✗ Kamera {i}: Nedostupna")
            
            cap.release()
            
        except Exception as e:
            print(f"  ✗ Kamera {i}: Chyba - {e}")
    
    return available_cameras

def test_qr_detection():
    """Test QR kod detekcie"""
    print("\n=== TEST QR KOD DETEKCIE ===")
    
    try:
        # Test ci OpenCV ma QR detektor
        qr_detector = cv2.QRCodeDetector()
        print("✓ OpenCV QR detektor dostupny")
        
        # Test s testovacim obrazom
        test_image = cv2.imread('test_qr.png')
        if test_image is not None:
            data, bbox, _ = qr_detector.detectAndDecode(test_image)
            if data:
                print(f"✓ Test QR kod nacitany: {data}")
            else:
                print("! Test QR kod nenajdeny v obraze")
        else:
            print("! Test QR obrazok nenajdeny (test_qr.png)")
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri teste QR detekcie: {e}")
        return False

def test_beeper():
    """Test beeper funkcionality"""
    print("\n=== TEST BEEPER ===")
    
    try:
        import winsound
        winsound.Beep(1000, 200)
        print("✓ Windows beeper dostupny")
        return True
    except ImportError:
        print("! Windows beeper nedostupny (nie je Windows)")
        return False
    except Exception as e:
        print(f"✗ Chyba pri teste beepera: {e}")
        return False

def test_dependencies():
    """Test zavislosti"""
    print("\n=== TEST ZAVISLOSTI ===")
    
    dependencies = [
        ('cv2', 'opencv-python'),
        ('numpy', 'numpy'),
        ('serial', 'pyserial')
    ]
    
    missing = []
    
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"✓ {package}: OK")
        except ImportError:
            print(f"✗ {package}: CHYBA - nie je nainstalovany")
            missing.append(package)
    
    if missing:
        print(f"\nPre instalovanie chybajucich zavislosti spustite:")
        print(f"pip install {' '.join(missing)}")
    
    return len(missing) == 0

def interactive_camera_test():
    """Interaktivny test kamery"""
    print("\n=== INTERAKTIVNY TEST KAMERY ===")
    
    available_cameras = test_cameras()
    if not available_cameras:
        print("Ziadne kamery dostupne pre test")
        return
    
    print(f"Dostupne kamery: {available_cameras}")
    
    try:
        camera_index = int(input("Vyberte index kamery pre test (Enter pre prvu dostupnu): ") or str(available_cameras[0]))
    except (ValueError, IndexError):
        camera_index = available_cameras[0]
    
    if camera_index not in available_cameras:
        print(f"Kamera {camera_index} nie je dostupna")
        return
    
    print(f"Spustam test kamery {camera_index}...")
    print("Stlacte 'q' pre ukoncenie testu")
    
    cap = cv2.VideoCapture(camera_index)
    qr_detector = cv2.QRCodeDetector()
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("Chyba pri citani z kamery")
                break
            
            # Test QR detekcie
            data, bbox, _ = qr_detector.detectAndDecode(frame)
            if data:
                print(f"QR kod najdeny: {data}")
                # Vykreslenie QR kodu
                if bbox is not None:
                    bbox = bbox.astype(int)
                    cv2.polylines(frame, [bbox], True, (0, 255, 0), 2)
                    cv2.putText(frame, data, (bbox[0][0], bbox[0][1] - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # Informacie na obraze
            cv2.putText(frame, f"Kamera {camera_index} - Stlac 'q' pre ukoncenie", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow(f'Test Kamery {camera_index}', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
    except KeyboardInterrupt:
        print("Test ukonceny")
    finally:
        cap.release()
        cv2.destroyAllWindows()

def main():
    """Hlavna funkcia"""
    print("=== TEST SETUP PRE KVALIFIKACNY PROGRAM ===")
    print("Verzia: 1.0")
    print("Datum: 2025-01-29")
    print("=" * 45)
    
    # Test zavislosti
    if not test_dependencies():
        print("\nCHYBA: Nie su nainstalovane vsetky potrebne zavislosti!")
        return
    
    # Test seriovych portov
    available_ports = test_serial_ports()
    
    # Test kamier
    available_cameras = test_cameras()
    
    # Test QR detekcie
    qr_ok = test_qr_detection()
    
    # Test beepera
    beeper_ok = test_beeper()
    
    # Suhrn
    print("\n=== SUHRN TESTOV ===")
    print(f"Seriove porty: {len(available_ports)} dostupnych")
    if available_ports:
        print(f"  Odporucany port: {available_ports[0]}")
    
    print(f"Kamery: {len(available_cameras)} dostupnych")
    if available_cameras:
        print(f"  Odporucana kamera: {available_cameras[-1]} (posledna = USB)")
    
    print(f"QR detekcia: {'OK' if qr_ok else 'PROBLEM'}")
    print(f"Beeper: {'OK' if beeper_ok else 'NEDOSTUPNY'}")
    
    # Odporucania
    print("\n=== ODPORUCANIA ===")
    if not available_ports:
        print("! Pripojte motordriver cez USB/seriovy port")
    
    if not available_cameras:
        print("! Pripojte USB kameru")
    elif len(available_cameras) == 1 and 0 in available_cameras:
        print("! Odporucame pripojit externu USB kameru (index 1)")
    
    if not qr_ok:
        print("! Skontrolujte instalaciu OpenCV")
    
    # Interaktivny test
    if available_cameras:
        test_camera = input("\nChcete spustit interaktivny test kamery? (y/N): ").lower().strip()
        if test_camera == 'y':
            interactive_camera_test()
    
    print("\n=== TEST UKONCENY ===")
    print("Mozete spustit kvalifikacny program: python kvalifikacia_demo.py")

if __name__ == "__main__":
    main()
