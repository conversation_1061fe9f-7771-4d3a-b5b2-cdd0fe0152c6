#!/usr/bin/env python3
"""
Logging Module for Robotic Vehicle
Centralized structured logging with time-stamping.
Logs events, warnings, errors, thread heartbeats, navigation decisions.
"""

import threading
import time
import json
import os
from datetime import datetime
from queue import Empty
from typing import Dict, List, Optional, TextIO


class LoggingModule:
    """Centralized logging module for structured logging with time-stamping."""
    
    def __init__(self):
        """Initialize the logging module."""
        self.module_name = "logging"
        
        # Logging configuration
        self.log_levels = {
            'DEBUG': 0,
            'INFO': 1,
            'WARNING': 2,
            'ERROR': 3,
            'CRITICAL': 4
        }
        self.current_log_level = 'INFO'
        self.log_to_console = True
        self.log_to_file = True
        
        # File logging configuration
        self.log_directory = "logs"
        self.log_filename_prefix = "robotour"
        self.max_log_file_size = 10 * 1024 * 1024  # 10MB
        self.max_log_files = 10
        self.current_log_file = None
        self.log_file_handle = None
        
        # Log formatting
        self.timestamp_format = "%Y-%m-%d %H:%M:%S.%f"
        self.include_thread_info = True
        self.structured_logging = True
        
        # Log statistics
        self.log_stats = {
            'total_logs': 0,
            'logs_by_level': {level: 0 for level in self.log_levels},
            'logs_by_module': {},
            'start_time': time.time(),
            'last_log_time': 0
        }
        
        # Log buffer for high-frequency logging
        self.log_buffer = []
        self.buffer_size = 1000
        self.buffer_flush_interval = 1.0  # seconds
        self.last_buffer_flush = 0
        
        # Logging status
        self.logging_faults = []
        self.file_write_errors = 0
        self.max_file_errors = 10
        
        # Thread control
        self.running = False
        
    def initialize_logging(self) -> bool:
        """Initialize logging system."""
        try:
            # Create log directory if it doesn't exist
            if self.log_to_file:
                os.makedirs(self.log_directory, exist_ok=True)
                
                # Create initial log file
                if not self.create_new_log_file():
                    return False
            
            # Log initialization
            self.log_message('logging', 'INFO', 'Logging system initialized')
            return True
            
        except Exception as e:
            self.add_fault(f"Error initializing logging: {e}")
            return False
    
    def create_new_log_file(self) -> bool:
        """Create a new log file."""
        try:
            # Close current file if open
            if self.log_file_handle:
                self.log_file_handle.close()
                self.log_file_handle = None
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.log_filename_prefix}_{timestamp}.log"
            filepath = os.path.join(self.log_directory, filename)
            
            # Open new log file
            self.log_file_handle = open(filepath, 'w', encoding='utf-8')
            self.current_log_file = filepath
            
            # Write header
            header = {
                'event': 'log_file_created',
                'timestamp': datetime.now().isoformat(),
                'filename': filename,
                'log_level': self.current_log_level
            }
            self.log_file_handle.write(json.dumps(header) + '\n')
            self.log_file_handle.flush()
            
            # Clean up old log files
            self.cleanup_old_log_files()
            
            return True
            
        except Exception as e:
            self.add_fault(f"Error creating log file: {e}")
            return False
    
    def cleanup_old_log_files(self):
        """Remove old log files to maintain file count limit."""
        try:
            if not os.path.exists(self.log_directory):
                return
            
            # Get all log files
            log_files = []
            for filename in os.listdir(self.log_directory):
                if filename.startswith(self.log_filename_prefix) and filename.endswith('.log'):
                    filepath = os.path.join(self.log_directory, filename)
                    stat = os.stat(filepath)
                    log_files.append((filepath, stat.st_mtime))
            
            # Sort by modification time (newest first)
            log_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove excess files
            if len(log_files) > self.max_log_files:
                for filepath, _ in log_files[self.max_log_files:]:
                    try:
                        os.remove(filepath)
                    except Exception as e:
                        self.add_fault(f"Error removing old log file {filepath}: {e}")
                        
        except Exception as e:
            self.add_fault(f"Error cleaning up log files: {e}")
    
    def should_log(self, level: str) -> bool:
        """Check if message should be logged based on current log level."""
        return self.log_levels.get(level, 0) >= self.log_levels.get(self.current_log_level, 1)
    
    def format_log_message(self, module: str, level: str, message: str, 
                          timestamp: float, extra_data: Optional[Dict] = None) -> Dict:
        """Format log message as structured data."""
        try:
            log_entry = {
                'timestamp': datetime.fromtimestamp(timestamp).strftime(self.timestamp_format)[:-3],
                'level': level,
                'module': module,
                'message': message,
                'unix_timestamp': timestamp
            }
            
            # Add thread information if enabled
            if self.include_thread_info:
                log_entry['thread'] = threading.current_thread().name
                log_entry['thread_id'] = threading.get_ident()
            
            # Add extra data if provided
            if extra_data:
                log_entry['data'] = extra_data
            
            return log_entry
            
        except Exception as e:
            # Fallback to simple format if structured formatting fails
            return {
                'timestamp': str(timestamp),
                'level': level,
                'module': module,
                'message': f"{message} (formatting error: {e})"
            }
    
    def log_message(self, module: str, level: str, message: str, 
                   timestamp: Optional[float] = None, extra_data: Optional[Dict] = None):
        """Log a message with specified level."""
        try:
            if not self.should_log(level):
                return
            
            if timestamp is None:
                timestamp = time.time()
            
            # Format message
            log_entry = self.format_log_message(module, level, message, timestamp, extra_data)
            
            # Update statistics
            self.update_log_stats(module, level)
            
            # Add to buffer
            self.log_buffer.append(log_entry)
            
            # Console output
            if self.log_to_console:
                self.write_to_console(log_entry)
            
            # Check if buffer needs flushing
            if (len(self.log_buffer) >= self.buffer_size or 
                time.time() - self.last_buffer_flush >= self.buffer_flush_interval):
                self.flush_log_buffer()
                
        except Exception as e:
            self.add_fault(f"Error logging message: {e}")
    
    def write_to_console(self, log_entry: Dict):
        """Write log entry to console."""
        try:
            if self.structured_logging:
                print(json.dumps(log_entry))
            else:
                # Simple format for console
                timestamp = log_entry.get('timestamp', '')
                level = log_entry.get('level', '')
                module = log_entry.get('module', '')
                message = log_entry.get('message', '')
                print(f"[{timestamp}] {level:8} {module:12} {message}")
                
        except Exception as e:
            # Fallback to basic print if formatting fails
            print(f"LOG ERROR: {e}")
    
    def flush_log_buffer(self):
        """Flush log buffer to file."""
        try:
            if not self.log_to_file or not self.log_file_handle or not self.log_buffer:
                return
            
            # Check file size and rotate if necessary
            if self.current_log_file and os.path.exists(self.current_log_file):
                file_size = os.path.getsize(self.current_log_file)
                if file_size >= self.max_log_file_size:
                    self.create_new_log_file()
            
            # Write buffered entries
            for log_entry in self.log_buffer:
                try:
                    self.log_file_handle.write(json.dumps(log_entry) + '\n')
                except Exception as e:
                    self.file_write_errors += 1
                    if self.file_write_errors <= self.max_file_errors:
                        self.add_fault(f"Error writing to log file: {e}")
            
            # Flush and clear buffer
            self.log_file_handle.flush()
            self.log_buffer.clear()
            self.last_buffer_flush = time.time()
            
        except Exception as e:
            self.add_fault(f"Error flushing log buffer: {e}")
    
    def update_log_stats(self, module: str, level: str):
        """Update logging statistics."""
        try:
            self.log_stats['total_logs'] += 1
            self.log_stats['last_log_time'] = time.time()
            
            # Update level statistics
            if level in self.log_stats['logs_by_level']:
                self.log_stats['logs_by_level'][level] += 1
            
            # Update module statistics
            if module not in self.log_stats['logs_by_module']:
                self.log_stats['logs_by_module'][module] = 0
            self.log_stats['logs_by_module'][module] += 1
            
        except Exception as e:
            self.add_fault(f"Error updating log stats: {e}")
    
    def get_log_stats(self) -> Dict:
        """Get current logging statistics."""
        try:
            current_time = time.time()
            uptime = current_time - self.log_stats['start_time']
            
            stats = self.log_stats.copy()
            stats['uptime_seconds'] = uptime
            stats['logs_per_second'] = self.log_stats['total_logs'] / uptime if uptime > 0 else 0
            stats['buffer_size'] = len(self.log_buffer)
            stats['current_log_file'] = self.current_log_file
            stats['file_write_errors'] = self.file_write_errors
            
            return stats
            
        except Exception as e:
            self.add_fault(f"Error getting log stats: {e}")
            return {}
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.logging_faults.append(fault)
        
        # Keep only recent faults (last 50)
        if len(self.logging_faults) > 50:
            self.logging_faults = self.logging_faults[-50:]
        
        # Also print to console as fallback
        print(f"LOGGING FAULT: {fault_message}")
    
    def get_status(self) -> Dict:
        """Get current logging status."""
        status = 'running'
        if self.logging_faults:
            status = 'error'
        elif self.file_write_errors > self.max_file_errors:
            status = 'file_error'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'log_level': self.current_log_level,
                'log_to_console': self.log_to_console,
                'log_to_file': self.log_to_file,
                'current_log_file': self.current_log_file,
                'buffer_size': len(self.log_buffer),
                'file_write_errors': self.file_write_errors,
                'statistics': self.get_log_stats(),
                'faults': self.logging_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'set_log_level':
                level = command.get('level', 'INFO')
                if level in self.log_levels:
                    self.current_log_level = level
                    self.log_message('logging', 'INFO', f'Log level changed to {level}')
            elif action == 'flush_buffer':
                self.flush_log_buffer()
            elif action == 'rotate_log_file':
                self.create_new_log_file()
            elif action == 'reset_faults':
                self.logging_faults.clear()
            elif action == 'reset_stats':
                self.log_stats = {
                    'total_logs': 0,
                    'logs_by_level': {level: 0 for level in self.log_levels},
                    'logs_by_module': {},
                    'start_time': time.time(),
                    'last_log_time': 0
                }
            else:
                self.add_fault(f"Unknown logging command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Initialize logging system
        if not self.initialize_logging():
            print("ERROR: Failed to initialize logging system")
        
        last_status_time = 0
        status_interval = 5.0  # Send status every 5 seconds
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Process log messages from other modules
                try:
                    while True:
                        log_msg = log_queue.get_nowait()
                        module = log_msg.get('module', 'unknown')
                        level = log_msg.get('level', 'INFO')
                        message = log_msg.get('message', '')
                        timestamp = log_msg.get('timestamp', current_time)
                        extra_data = log_msg.get('data')
                        
                        self.log_message(module, level, message, timestamp, extra_data)
                except Empty:
                    pass
                
                # Periodic buffer flush
                if current_time - self.last_buffer_flush >= self.buffer_flush_interval:
                    self.flush_log_buffer()
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.1)
                
        except Exception as e:
            self.add_fault(f'Logging module error: {e}')
        finally:
            # Final buffer flush
            self.flush_log_buffer()
            
            # Close log file
            if self.log_file_handle:
                try:
                    self.log_file_handle.close()
                except:
                    pass
            
            self.running = False
            print("Logging module stopped")
