# QR Kody pre Kvalifikacny Program

Program caka na nacitanie lubovolneho QR kodu pre spustenie. Tu su niektore priklady QR kodov, ktore mozete pouzit:

## Jednoduche QR Kody

### START
```
START
```

### ROBOTOUR
```
ROBOTOUR
```

### GO
```
GO
```

### TEST
```
TEST
```

## Ako Vytvorit QR Kod

### Online Generatory
1. Idite na https://qr-code-generator.com/
2. Zadajte text (napr. "START")
3. Stiahnite QR kod ako obrazok
4. Vytlacte na papier alebo zobrazite na obrazovke

### Python Generator (volitelne)
```python
import qrcode

# Vytvorenie QR kodu
qr = qrcode.QRCode(version=1, box_size=10, border=5)
qr.add_data("START")
qr.make(fit=True)

# Ulozenie ako obrazok
img = qr.make_image(fill_color="black", back_color="white")
img.save("start_qr.png")
```

## Tipy pre Pouzitie

### Kvalita QR Kodu
- Pouzite kontrastne farby (cierne na bielom)
- QR kod by mal byt dostatocne velky (min. 3x3 cm)
- Zabezpecte dobre osvetlenie
- QR kod by mal byt rovny (nie nakriveny)

### Umiestnenie
- Umiestnite QR kod do centra kamery
- Vzdialenost cca 20-50 cm od kamery
- QR kod by mal byt ostry (nie rozmazany)

### Testovanie
- Pouzite test_setup.py pre overenie detekcie
- Skuste rozne QR kody ak jeden nefunguje
- Mozete pouzit aj QR kod z mobilneho telefonu

## Obsah QR Kodu

Program akceptuje lubovolny obsah QR kodu. Odporucane su kratke texty:
- START
- GO
- ROBOTOUR
- TEST
- 123
- OK

Program len detekuje pritomnost QR kodu, obsah sa pouzije len pre logovanie.

## Riešenie Problemov

### QR Kod sa Necita
1. Skontrolujte osvetlenie
2. Priblizite/vzdialte QR kod
3. Zabezpecte ze QR kod je ostry
4. Skuste iny QR kod
5. Pouzite test_setup.py pre diagnostiku

### Kamera Nevidi QR Kod
1. Overte ze kamera funguje (test_setup.py)
2. Skontrolujte index kamery (0, 1, 2...)
3. Zatvorte ine programy pouzivajuce kameru
4. Restartujte program

---

**Poznamka**: Pre kvalifikaciu staci lubovolny QR kod. Program len caka na jeho detekciu pre spustenie sekvencie.
