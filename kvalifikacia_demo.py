#!/usr/bin/env python3
"""
Kvalifikacny program pre Robotour
Integruje motordriver a camera kniznice pre kvalifikacnu jazdu.

Program:
1. Caka na nacitanie QR kodu z kamery
2. Po nacitani pipne beeper a odpocitava 3s
3. Zacne jazdu rychlostou 10% dopredu
4. <PERSON>ri detek<PERSON><PERSON> prekazky (0-1m) sa zastavi na 10s
5. Ak prekazka zmizne, pokracuje v jazde
6. Ak prekazka nezmizne, zacne obchadzaci manever

Autor: Robotour Team
Datum: 2025-01-29
"""

import sys
import os
import time
import threading
import logging
import cv2
import numpy as np
from typing import Optional
import winsound  # Pre Windows beeper

# Pridanie ciest k kniznicam
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'motordriver'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'camera', 'camera'))

from robot_driver import RobotDriver
from camera_module import CameraModule, Direction, CameraResult

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kvalifikacia.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QRCodeDetector:
    """Trieda pre detekciu QR kodov"""
    
    def __init__(self):
        self.qr_detector = cv2.QRCodeDetector()
        logger.info("QR kod detektor inicializovany")
    
    def detect_qr_code(self, frame: np.ndarray) -> Optional[str]:
        """
        Detekuje QR kod v snimke
        
        Args:
            frame: Snimka z kamery
            
        Returns:
            Obsah QR kodu alebo None ak nie je najdeny
        """
        try:
            # Detekcia QR kodu
            data, bbox, _ = self.qr_detector.detectAndDecode(frame)
            
            if data:
                logger.info(f"QR kod najdeny: {data}")
                return data
            return None
            
        except Exception as e:
            logger.error(f"Chyba pri detekcii QR kodu: {e}")
            return None

class KvalifikacnyProgram:
    """Hlavna trieda pre kvalifikacny program"""
    
    def __init__(self, motor_port='COM4', camera_index=1):
        """
        Inicializacia kvalifikacneho programu

        Args:
            motor_port: Port pre motordriver
            camera_index: Index kamery (1 pre USB kameru)
        """
        self.motor_port = motor_port
        self.camera_index = camera_index

        # Komponenty
        self.robot = None
        self.camera = None
        self.qr_detector = QRCodeDetector()

        # Stavy programu
        self.running = False
        self.started = False
        self.obstacle_start_time = None
        self.total_distance = 0.0
        self.target_distance = 10.0  # 10 metrov
        self.speed = 10  # 10% rychlost

        # Konstanty
        self.OBSTACLE_WAIT_TIME = 10.0  # 10 sekund cakania
        self.COUNTDOWN_TIME = 3  # 3 sekundy odpocitavania
        self.OBSTACLE_DISTANCE_THRESHOLD = 1.0  # 1 meter

        logger.info("Kvalifikacny program inicializovany")
    
    def beep(self, frequency=1000, duration=500):
        """
        Pipnutie beepera
        
        Args:
            frequency: Frekvencia v Hz
            duration: Dlzka v ms
        """
        try:
            winsound.Beep(frequency, duration)
        except Exception as e:
            logger.warning(f"Beeper nedostupny: {e}")
            print("BEEP!")  # Fallback pre systemy bez beepera
    
    def initialize_components(self) -> bool:
        """Inicializacia vsetkych komponentov"""
        try:
            # Inicializacia motordriver
            logger.info(f"Inicializujem motordriver na porte {self.motor_port}")
            self.robot = RobotDriver(self.motor_port, baudrate=9600)
            self.robot.start()
            self.robot.stop_motors()  # Zastavenie na zaciatku
            logger.info("Motordriver uspesne inicializovany")
            
            # Inicializacia kamery
            logger.info(f"Inicializujem kameru s indexom {self.camera_index}")
            self.camera = CameraModule(camera_index=self.camera_index)
            if not self.camera.initialize():
                logger.error("Nepodarilo sa inicializovat kameru")
                return False
            logger.info("Kamera uspesne inicializovana")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri inicializacii komponentov: {e}")
            return False
    
    def wait_for_qr_code(self) -> bool:
        """
        Caka na nacitanie QR kodu
        
        Returns:
            True ak bol QR kod najdeny
        """
        logger.info("Cakam na nacitanie QR kodu...")
        print("=== CAKANIE NA QR KOD ===")
        print("Ukaz QR kod do kamery pre spustenie programu")
        print("Stlac 'q' pre ukoncenie")
        
        while self.running:
            # Zachytenie snimky
            frame = self.camera.capture_frame()
            if frame is None:
                continue
            
            # Detekcia QR kodu
            qr_data = self.qr_detector.detect_qr_code(frame)
            if qr_data:
                logger.info(f"QR kod nacitany: {qr_data}")
                self.beep(1000, 200)  # Kratky pip
                return True
            
            # Zobrazenie kamery
            cv2.imshow('Kvalifikacia - Cakanie na QR kod', frame)
            
            # Kontrola ukoncenia
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                logger.info("Program ukonceny uzivatelom")
                return False
        
        return False
    
    def countdown(self):
        """Odpocitavanie 3 sekund"""
        logger.info("Zacinam odpocitavanie...")
        print("=== ODPOCITAVANIE ===")

        for i in range(self.COUNTDOWN_TIME, 0, -1):
            print(f"Start za {i} sekund...")
            logger.info(f"Odpocitavam: {i}")
            self.beep(800, 300)  # Pip pre kazdu sekundu
            time.sleep(1)

        print("START!")
        logger.info("START - zacinam jazdu")
        self.beep(1200, 1000)  # Dlhy pip pre start
    
    def estimate_distance(self, speed_percent: int, time_seconds: float) -> float:
        """
        Odhad prejdenej vzdialenosti
        
        Args:
            speed_percent: Rychlost v percentach
            time_seconds: Cas jazdy v sekundach
            
        Returns:
            Odhad vzdialenosti v metroch
        """
        # Jednoduchy odhad: 10% rychlost = cca 0.5 m/s
        estimated_speed_ms = (speed_percent / 100.0) * 0.5
        return estimated_speed_ms * time_seconds
    
    def drive_sequence(self):
        """Hlavna sekvencia jazdy"""
        logger.info("Zacinam hlavnu sekvenciu jazdy")
        print("=== JAZDA ===")
        
        start_time = time.time()
        last_distance_update = start_time
        
        while self.running and self.total_distance < self.target_distance:
            current_time = time.time()
            
            # Zachytenie snimky z kamery
            frame = self.camera.capture_frame()
            if frame is None:
                continue
            
            # Spracovanie snimky
            result = self.camera.process_frame(frame)

            # Aktualizacia prejdenej vzdialenosti
            if current_time - last_distance_update >= 0.5:  # Kazdu pol sekundu
                time_diff = current_time - last_distance_update
                distance_increment = self.estimate_distance(self.speed, time_diff)
                self.total_distance += distance_increment
                last_distance_update = current_time

                logger.info(f"Prejdena vzdialenost: {self.total_distance:.2f}m / {self.target_distance}m")

            # Spracovanie detekcie prekazky
            if result.obstacle_detected:
                logger.warning("Prekazka detekovana!")
                self.handle_obstacle()
                if not self.running:
                    break
            else:
                # Normalna jazda dopredu
                self.robot.move_forward(self.speed)
            
            # Zobrazenie kamery s informaciami
            self.display_camera_info(frame, result)
            
            # Kontrola ukoncenia
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                logger.info("Jazda ukoncena uzivatelom")
                break
        
        # Zastavenie na konci - zabezpecime ze sa kolesa zastavia
        self.robot.stop_motors()
        time.sleep(0.5)  # Kratke cakanie
        self.robot.stop_motors()  # Druhe zastavenie pre istotu
        logger.info(f"Jazda ukoncena. Celkova vzdialenost: {self.total_distance:.2f}m")
        print(f"=== JAZDA UKONCENA ===")
        print(f"Prejdena vzdialenost: {self.total_distance:.2f}m")
        print("Robot zastaveny - kolesa sa prestali tocit")
    
    def handle_obstacle(self):
        """Spracovanie detekcie prekazky"""
        logger.info("Spracovavam detekciu prekazky")
        
        # Zastavenie robota
        self.robot.stop_motors()
        
        if self.obstacle_start_time is None:
            self.obstacle_start_time = time.time()
            logger.info("Zastavujem na 10 sekund kvoli prekazke")
            print("=== PREKAZKA DETEKOVANA ===")
            print("Robot zastaveny na 10 sekund...")
        
        # Cakanie 10 sekund
        elapsed = time.time() - self.obstacle_start_time
        remaining = self.OBSTACLE_WAIT_TIME - elapsed
        
        if remaining > 0:
            print(f"Cakam este {remaining:.1f} sekund...")
            return
        
        # Po 10 sekundach - kontrola ci prekazka stale existuje
        frame = self.camera.capture_frame()
        if frame is not None:
            result = self.camera.process_frame(frame)

            if not result.obstacle_detected:
                # Prekazka zmizla - pokracuj v jazde
                logger.info("Prekazka zmizla, pokracujem v jazde")
                print("Prekazka zmizla - pokracujem v jazde")
                self.obstacle_start_time = None
            else:
                # Prekazka stale existuje - obchadzaci manever
                logger.info("Prekazka stale existuje - zacinam obchadzaci manever")
                print("Prekazka stale existuje - obchadzaci manever")
                self.perform_bypass_maneuver()
                self.obstacle_start_time = None
    
    def perform_bypass_maneuver(self):
        """Jednoduchy obchadzaci manever s 10% rychlostou"""
        logger.info("Vykonavam obchadzaci manever")
        print("=== OBCHADZACI MANEVER ===")

        try:
            # Otocenie doprava - pouzijeme 10% rychlost
            print("Otacam doprava...")
            self.robot.turn_right(self.speed)  # 10% namiesto 30%
            time.sleep(2)

            # Jazda dopredu
            print("Jazda dopredu...")
            self.robot.move_forward(self.speed)
            time.sleep(3)

            # Otocenie dolava
            print("Otacam dolava...")
            self.robot.turn_left(self.speed)  # 10% namiesto 30%
            time.sleep(2)

            # Jazda dopredu
            print("Jazda dopredu...")
            self.robot.move_forward(self.speed)
            time.sleep(3)

            # Otocenie dolava (navrat na povodny smer)
            print("Navrat na povodny smer...")
            self.robot.turn_left(self.speed)  # 10% namiesto 30%
            time.sleep(2)

            logger.info("Obchadzaci manever ukonceny")
            print("Obchadzaci manever ukonceny")

        except Exception as e:
            logger.error(f"Chyba pri obchadzacom manevri: {e}")
    
    def display_camera_info(self, frame: np.ndarray, result: CameraResult):
        """Zobrazenie kamery s informaciami"""
        vis_frame = frame.copy()
        height, width = vis_frame.shape[:2]

        # Textove informacie
        info_texts = [
            f"Vzdialenost: {self.total_distance:.1f}m / {self.target_distance}m",
            f"Rychlost: {self.speed}%",
            f"Smer: {result.direction.value.upper()}",
            f"Prekazka: {'ANO' if result.obstacle_detected else 'NIE'}",
            f"Spolahlivos: {result.confidence:.2f}"
        ]
        
        # Kreslenie textu
        y_offset = 30
        for i, text in enumerate(info_texts):
            color = (0, 0, 255) if result.obstacle_detected else (0, 255, 0)
            cv2.putText(vis_frame, text, (10, y_offset + i * 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # Progress bar pre vzdialenost
        progress = min(self.total_distance / self.target_distance, 1.0)
        bar_width = int(progress * (width - 20))
        cv2.rectangle(vis_frame, (10, height - 30), (10 + bar_width, height - 10), (0, 255, 0), -1)
        cv2.rectangle(vis_frame, (10, height - 30), (width - 10, height - 10), (255, 255, 255), 2)
        
        cv2.imshow('Kvalifikacia - Jazda', vis_frame)
    
    def run(self):
        """Hlavna funkcia programu"""
        logger.info("Spustam kvalifikacny program")
        print("=== KVALIFIKACNY PROGRAM ROBOTOUR ===")
        print("Verzia: 1.0")
        print("Datum: 2025-01-29")
        print("=====================================")
        
        self.running = True
        
        try:
            # Inicializacia komponentov
            if not self.initialize_components():
                logger.error("Nepodarilo sa inicializovat komponenty")
                return False
            
            # Cakanie na QR kod
            if not self.wait_for_qr_code():
                return False
            
            # Odpocitavanie
            self.countdown()
            
            # Hlavna jazda
            self.drive_sequence()
            
            logger.info("Kvalifikacny program uspesne ukonceny")
            return True
            
        except KeyboardInterrupt:
            logger.info("Program ukonceny cez Ctrl+C")
            return False
        except Exception as e:
            logger.error(f"Neocakavana chyba: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Ukoncenie a upratanie"""
        logger.info("Ukoncujem program...")
        self.running = False
        
        if self.robot:
            self.robot.stop_motors()
            time.sleep(0.2)  # Kratke cakanie
            self.robot.stop_motors()  # Druhe zastavenie pre istotu
            self.robot.stop()
            logger.info("Motordriver ukonceny")
        
        if self.camera:
            self.camera.cleanup()
            logger.info("Kamera ukoncena")
        
        cv2.destroyAllWindows()
        print("=== PROGRAM UKONCENY ===")

def main():
    """Hlavna funkcia"""
    print("Kvalifikacny program pre Robotour")
    print("Stlac Enter pre pokracovanie alebo Ctrl+C pre ukoncenie...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("Program ukonceny")
        return
    
    # Moznost vybrania portov
    print("\nNastavenie:")
    motor_port = input("Motor port (Enter pre COM4): ").strip() or "COM4"
    camera_index = input("Camera index (Enter pre 1): ").strip() or "1"
    
    try:
        camera_index = int(camera_index)
    except ValueError:
        camera_index = 1
    
    # Spustenie programu
    program = KvalifikacnyProgram(motor_port=motor_port, camera_index=camera_index)
    program.run()

if __name__ == "__main__":
    main()
