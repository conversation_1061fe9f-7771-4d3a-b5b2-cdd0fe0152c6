#!/usr/bin/env python3
"""
Kvalifikacny program pre Robotour
Integruje motordriver a camera kniznice pre kvalifikacnu jazdu.

Program:
1. Caka na nacitanie QR kodu z kamery
2. Po nacitani pipne beeper a odpocitava 3s
3. Zacne jazdu rychlostou 10% dopredu
4. <PERSON>ri detek<PERSON><PERSON> prekazky (0-1m) sa zastavi na 10s
5. Ak prekazka zmizne, pokracuje v jazde
6. Ak prekazka nezmizne, zacne obchadzaci manever

Autor: Robotour Team
Datum: 2025-01-29
"""

import sys
import os
import time
import threading
import logging
import cv2
import numpy as np
from typing import Optional
import winsound  # Pre Windows beeper
from datetime import datetime

# Pridanie ciest k kniznicam
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'motordriver'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'camera', 'camera'))

from robot_driver import RobotDriver
from camera_module import CameraModule, Direction, CameraResult

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kvalifikacia.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QRCodeDetector:
    """Trieda pre detekciu QR kodov"""
    
    def __init__(self):
        self.qr_detector = cv2.QRCodeDetector()
        logger.info("QR kod detektor inicializovany")
    
    def detect_qr_code(self, frame: np.ndarray) -> Optional[str]:
        """
        Detekuje QR kod v snimke
        
        Args:
            frame: Snimka z kamery
            
        Returns:
            Obsah QR kodu alebo None ak nie je najdeny
        """
        try:
            # Detekcia QR kodu
            data, bbox, _ = self.qr_detector.detectAndDecode(frame)
            
            if data:
                logger.info(f"QR kod najdeny: {data}")
                return data
            return None
            
        except Exception as e:
            logger.error(f"Chyba pri detekcii QR kodu: {e}")
            return None

class KvalifikacnyProgram:
    """Hlavna trieda pre kvalifikacny program"""

    def __init__(self, motor_port='COM4', camera_index=1, obstacle_threshold=20.0):
        """
        Inicializacia kvalifikacneho programu

        Args:
            motor_port: Port pre motordriver
            camera_index: Index kamery (1 pre USB kameru)
            obstacle_threshold: Prah detekcie prekazky v percentach obrazovej plochy (default 20%)
        """
        self.motor_port = motor_port
        self.camera_index = camera_index
        self.obstacle_threshold_percent = obstacle_threshold

        # Komponenty
        self.robot = None
        self.camera = None
        self.qr_detector = QRCodeDetector()

        # Stavy programu
        self.running = False
        self.started = False
        self.driving_started = False  # Flag pre zapnutie detekcie prekazok
        self.driving_start_time = None  # Cas zaciatku jazdy
        self.obstacle_start_time = None
        self.total_distance = 0.0
        self.target_distance = 10.0  # 10 metrov
        self.speed = 10  # 10% rychlost
        self.BLIND_DRIVE_TIME = 3.0  # 3 sekundy "naslepo" bez detekcie

        # Logovanie a screenshoty
        self.log_dir = "logs"
        self.create_log_directory()
        self.screenshot_counter = 0

        # Konstanty
        self.OBSTACLE_WAIT_TIME = 10.0  # 10 sekund cakania
        self.COUNTDOWN_TIME = 3  # 3 sekundy odpocitavania
        self.OBSTACLE_DISTANCE_THRESHOLD = 1.0  # 1 meter

        logger.info("Kvalifikacny program inicializovany")

    def create_log_directory(self):
        """Vytvorenie adresara pre logy a screenshoty"""
        try:
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)
                logger.info(f"Vytvoreny log adresar: {self.log_dir}")
        except Exception as e:
            logger.error(f"Chyba pri vytvarani log adresara: {e}")

    def save_screenshot(self, frame: np.ndarray, prefix: str, description: str = ""):
        """Ulozenie screenshotu s popisom"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.screenshot_counter += 1
            filename = f"{prefix}_{timestamp}_{self.screenshot_counter:03d}.jpg"
            filepath = os.path.join(self.log_dir, filename)

            # Pridanie popisu na obrazok
            if description:
                frame_copy = frame.copy()
                cv2.putText(frame_copy, description, (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                cv2.imwrite(filepath, frame_copy)
            else:
                cv2.imwrite(filepath, frame)

            logger.info(f"Screenshot ulozeny: {filepath} - {description}")
            return filepath
        except Exception as e:
            logger.error(f"Chyba pri ukladani screenshotu: {e}")
            return None
    
    def beep(self, frequency=1000, duration=500):
        """
        Pipnutie beepera
        
        Args:
            frequency: Frekvencia v Hz
            duration: Dlzka v ms
        """
        try:
            winsound.Beep(frequency, duration)
        except Exception as e:
            logger.warning(f"Beeper nedostupny: {e}")
            print("BEEP!")  # Fallback pre systemy bez beepera
    
    def initialize_components(self) -> bool:
        """Inicializacia vsetkych komponentov"""
        try:
            # Inicializacia motordriver
            logger.info(f"Inicializujem motordriver na porte {self.motor_port}")
            self.robot = RobotDriver(self.motor_port, baudrate=9600)
            self.robot.start()
            self.robot.stop_motors()  # Zastavenie na zaciatku
            logger.info("Motordriver uspesne inicializovany")
            
            # Inicializacia kamery
            logger.info(f"Inicializujem kameru s indexom {self.camera_index}")
            self.camera = CameraModule(camera_index=self.camera_index)
            if not self.camera.initialize():
                logger.error("Nepodarilo sa inicializovat kameru")
                return False
            logger.info("Kamera uspesne inicializovana")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri inicializacii komponentov: {e}")
            return False
    
    def wait_for_qr_code(self) -> bool:
        """
        Caka na nacitanie QR kodu
        
        Returns:
            True ak bol QR kod najdeny
        """
        logger.info("Cakam na nacitanie QR kodu...")
        print("=== CAKANIE NA QR KOD ===")
        print("Ukaz QR kod do kamery pre spustenie programu")
        print("Stlac 'q' pre ukoncenie")
        
        while self.running:
            # Zachytenie snimky
            frame = self.camera.capture_frame()
            if frame is None:
                continue
            
            # Detekcia QR kodu
            qr_data = self.qr_detector.detect_qr_code(frame)
            if qr_data:
                logger.info(f"QR kod nacitany: {qr_data}")
                self.beep(1000, 200)  # Kratky pip
                return True
            
            # Zobrazenie kamery
            cv2.imshow('Kvalifikacia - Cakanie na QR kod', frame)
            
            # Kontrola ukoncenia
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                logger.info("Program ukonceny uzivatelom")
                return False
        
        return False
    
    def countdown(self):
        """Odpocitavanie 3 sekund"""
        logger.info("Zacinam odpocitavanie...")
        print("=== ODPOCITAVANIE ===")

        for i in range(self.COUNTDOWN_TIME, 0, -1):
            print(f"Start za {i} sekund...")
            logger.info(f"Odpocitavam: {i}")
            self.beep(800, 300)  # Pip pre kazdu sekundu
            time.sleep(1)

        print("START!")
        logger.info("START - zacinam jazdu")
        self.beep(1200, 1000)  # Dlhy pip pre start

        # Zaznamenanie casu zaciatku jazdy
        self.driving_start_time = time.time()
        self.driving_started = True
        logger.info(f"Jazda zacata - detekcia prekazok sa zapne po {self.BLIND_DRIVE_TIME}s")
    
    def estimate_distance(self, speed_percent: int, time_seconds: float) -> float:
        """
        Odhad prejdenej vzdialenosti
        
        Args:
            speed_percent: Rychlost v percentach
            time_seconds: Cas jazdy v sekundach
            
        Returns:
            Odhad vzdialenosti v metroch
        """
        # Jednoduchy odhad: 10% rychlost = cca 0.5 m/s
        estimated_speed_ms = (speed_percent / 100.0) * 0.5
        return estimated_speed_ms * time_seconds

    def detect_simple_obstacle(self, frame: np.ndarray) -> tuple[bool, float]:
        """
        JEDNODUCHA a SPOLAHLIVA detekcia prekazky
        Detekuje len VELKE tmave objekty v STREDE obrazu

        Args:
            frame: Snimka z kamery

        Returns:
            tuple: (is_obstacle, coverage) - True ak je detekovana prekazka
        """
        try:
            height, width = frame.shape[:2]

            # VELMI MALA stredova oblast - len 20% sirky, 50% vysky
            center_width = width // 5  # 20% sirky
            center_height = height // 2  # 50% vysky

            center_x_start = (width - center_width) // 2
            center_x_end = center_x_start + center_width
            center_y_start = height // 4  # Zacina od 25% vysky
            center_y_end = center_y_start + center_height

            # ROI - maly stredovy obdlznik
            roi = frame[center_y_start:center_y_end, center_x_start:center_x_end]

            if roi.size == 0:
                return False, 0.0

            # Konverzia na grayscale
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # JEDNODUCHY prah - detekcia tmavych oblastí
            # Objekty su zvycajne tmavšie ako pozadie
            _, binary = cv2.threshold(gray_roi, 60, 255, cv2.THRESH_BINARY_INV)

            # Vypocet pokrytia
            total_pixels = binary.shape[0] * binary.shape[1]
            dark_pixels = cv2.countNonZero(binary)
            coverage_percent = (dark_pixels / total_pixels) * 100

            # VYSOKA hranica - detekuje len VELKE objekty
            is_obstacle = coverage_percent > 40.0  # 40% ROI musi byt tmave

            # Logovanie len pri detekcii
            if is_obstacle:
                logger.warning(f"JEDNODUCHA PREKAZKA! ROI pokrytie: {coverage_percent:.1f}%")
                # Screenshot s vyznacenou ROI
                debug_frame = frame.copy()
                cv2.rectangle(debug_frame, (center_x_start, center_y_start),
                             (center_x_end, center_y_end), (0, 0, 255), 3)
                cv2.putText(debug_frame, f"PREKAZKA {coverage_percent:.1f}%", (10, 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                self.save_screenshot(debug_frame, "simple_obstacle", f"ROI {coverage_percent:.1f}%")
            else:
                # Logovanie len kazdu 5. sekundu
                current_time = time.time()
                if not hasattr(self, '_last_log_time') or current_time - self._last_log_time > 5:
                    self._last_log_time = current_time
                    logger.info(f"Detekcia OK: ROI pokrytie {coverage_percent:.1f}% (prah 40%)")

            return is_obstacle, coverage_percent

        except Exception as e:
            logger.error(f"Chyba pri jednoduchej detekcii: {e}")
            return False, 0.0
    
    def drive_sequence(self):
        """Hlavna sekvencia jazdy"""
        logger.info("Zacinam hlavnu sekvenciu jazdy")
        print("=== JAZDA ===")
        
        start_time = time.time()
        last_distance_update = start_time
        
        while self.running and self.total_distance < self.target_distance:
            current_time = time.time()
            
            # Zachytenie snimky z kamery
            frame = self.camera.capture_frame()
            if frame is None:
                continue
            
            # Spracovanie snimky - pouzijeme nasu vlastnu detekciu velkej prekazky
            result = self.camera.process_frame(frame)

            # Aktualizacia prejdenej vzdialenosti
            if current_time - last_distance_update >= 0.5:  # Kazdu pol sekundu
                time_diff = current_time - last_distance_update
                distance_increment = self.estimate_distance(self.speed, time_diff)
                self.total_distance += distance_increment
                last_distance_update = current_time

                logger.info(f"Prejdena vzdialenost: {self.total_distance:.2f}m / {self.target_distance}m")

            # Spracovanie detekcie prekazky - LEN ak uz jazda zacala A uplynuli 3 sekundy
            if self.driving_started:
                # Kontrola ci uplynuli 3 sekundy od zaciatku jazdy
                time_since_start = current_time - self.driving_start_time
                detection_active = time_since_start >= self.BLIND_DRIVE_TIME

                if detection_active:
                    simple_obstacle_detected, obstacle_coverage = self.detect_simple_obstacle(frame)

                    if simple_obstacle_detected:
                        logger.warning(f"JEDNODUCHA PREKAZKA DETEKOVANA! ROI pokrytie: {obstacle_coverage:.1f}%")
                        self.handle_obstacle(frame, obstacle_coverage)
                        if not self.running:
                            break
                    else:
                        # Normalna jazda dopredu
                        self.robot.move_forward(self.speed)

                        # Kazdu 10. sekundu ulozime screenshot pre monitoring
                        if int(current_time) % 10 == 0 and int(current_time) != getattr(self, '_last_monitor_time', 0):
                            self._last_monitor_time = int(current_time)
                            self.save_screenshot(frame, "normal_drive", f"Jazda {self.total_distance:.1f}m")
                else:
                    # Prvych 3 sekund - jazda "naslepo" bez detekcie
                    remaining_blind_time = self.BLIND_DRIVE_TIME - time_since_start
                    logger.info(f"Jazda NASLEPO - este {remaining_blind_time:.1f}s do zapnutia detekcie")
                    print(f"Jazda naslepo - detekcia za {remaining_blind_time:.1f}s")
                    self.robot.move_forward(self.speed)
            else:
                # Pred zaciatkom jazdy - len zobrazujeme kameru, bez detekcie prekazok
                pass
            
            # Zobrazenie kamery s informaciami
            self.display_camera_info(frame, result)
            
            # Kontrola ukoncenia
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                logger.info("Jazda ukoncena uzivatelom")
                break
        
        # Zastavenie na konci - zabezpecime ze sa kolesa zastavia
        self.robot.stop_motors()
        time.sleep(0.5)  # Kratke cakanie
        self.robot.stop_motors()  # Druhe zastavenie pre istotu
        logger.info(f"Jazda ukoncena. Celkova vzdialenost: {self.total_distance:.2f}m")
        print(f"=== JAZDA UKONCENA ===")
        print(f"Prejdena vzdialenost: {self.total_distance:.2f}m")
        print("Robot zastaveny - kolesa sa prestali tocit")
    
    def handle_obstacle(self, frame: np.ndarray, obstacle_percentage: float):
        """Spracovanie detekcie prekazky - len cakanie, bez obchadzania"""
        logger.warning(f"SPRACOVAVAM DETEKCIU PREKAZKY - {obstacle_percentage:.1f}%")

        # OKAMZITE zastavenie robota - BEZPECNOST!
        self.robot.stop_motors()
        time.sleep(0.1)  # Kratka pauza
        self.robot.stop_motors()  # Druhe zastavenie pre istotu

        if self.obstacle_start_time is None:
            self.obstacle_start_time = time.time()
            logger.warning(f"ROBOT ZASTAVENY! Prekazka zabera {obstacle_percentage:.1f}% obrazu")
            print("=== PREKAZKA DETEKOVANA ===")
            print(f"Robot zastaveny na 10 sekund... Prekazka: {obstacle_percentage:.1f}%")

            # Ulozenie screenshotu pri zastaveni
            self.save_screenshot(frame, "obstacle_stop", f"STOP - Prekazka {obstacle_percentage:.1f}%")

        # Cakanie 10 sekund
        elapsed = time.time() - self.obstacle_start_time
        remaining = self.OBSTACLE_WAIT_TIME - elapsed

        if remaining > 0:
            print(f"Cakam este {remaining:.1f} sekund...")
            # Kazdu sekundu kontrolujeme ci prekazka stale existuje
            if int(remaining) != getattr(self, '_last_check_time', -1):
                self._last_check_time = int(remaining)
                current_frame = self.camera.capture_frame()
                if current_frame is not None:
                    still_obstacle, current_coverage = self.detect_simple_obstacle(current_frame)
                    logger.info(f"Kontrola prekazky: ROI {current_coverage:.1f}% (stale prekazka: {still_obstacle})")
            return

        # Po 10 sekundach - ulozime screenshot a pokracujeme
        final_frame = self.camera.capture_frame()
        if final_frame is not None:
            _, final_coverage = self.detect_simple_obstacle(final_frame)
            self.save_screenshot(final_frame, "obstacle_after", f"Po cakani - {final_coverage:.1f}%")
            logger.warning(f"10 sekund uplynulo - pokracujem v jazde. Finalna detekcia: ROI {final_coverage:.1f}%")

        print("10 sekund uplynulo - pokracujem v jazde")
        self.obstacle_start_time = None
    

    
    def display_camera_info(self, frame: np.ndarray, result: CameraResult):
        """Zobrazenie kamery s informaciami"""
        vis_frame = frame.copy()
        height, width = vis_frame.shape[:2]

        # Detekcia prekazky v strede pre zobrazenie - LEN ak jazda zacala A uplynuli 3s
        if self.driving_started and self.driving_start_time:
            time_since_start = time.time() - self.driving_start_time
            detection_active = time_since_start >= self.BLIND_DRIVE_TIME

            if detection_active:
                simple_obstacle, obstacle_coverage = self.detect_simple_obstacle(frame)
                detection_status = "AKTIVNA"
            else:
                simple_obstacle, obstacle_coverage = False, 0.0
                remaining_time = self.BLIND_DRIVE_TIME - time_since_start
                detection_status = f"NASLEPO ({remaining_time:.1f}s)"
        else:
            simple_obstacle, obstacle_coverage = False, 0.0
            detection_status = "VYPNUTA"

        # Textove informacie
        info_texts = [
            f"Vzdialenost: {self.total_distance:.1f}m / {self.target_distance}m",
            f"Rychlost: {self.speed}%",
            f"Smer: {result.direction.value.upper()}",
            f"Detekcia: {detection_status}",
            f"Prekazka: {'ANO' if simple_obstacle else 'NIE'}",
            f"ROI pokrytie: {obstacle_coverage:.1f}%",
            f"Spolahlivos: {result.confidence:.2f}"
        ]

        # Kreslenie textu
        y_offset = 30
        for i, text in enumerate(info_texts):
            color = (0, 0, 255) if simple_obstacle else (0, 255, 0)
            cv2.putText(vis_frame, text, (10, y_offset + i * 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # Progress bar pre vzdialenost
        progress = min(self.total_distance / self.target_distance, 1.0)
        bar_width = int(progress * (width - 20))
        cv2.rectangle(vis_frame, (10, height - 30), (10 + bar_width, height - 10), (0, 255, 0), -1)
        cv2.rectangle(vis_frame, (10, height - 30), (width - 10, height - 10), (255, 255, 255), 2)
        
        cv2.imshow('Kvalifikacia - Jazda', vis_frame)
    
    def run(self):
        """Hlavna funkcia programu"""
        logger.info("Spustam kvalifikacny program")
        print("=== KVALIFIKACNY PROGRAM ROBOTOUR ===")
        print("Verzia: 1.0")
        print("Datum: 2025-01-29")
        print("=====================================")
        
        self.running = True
        
        try:
            # Inicializacia komponentov
            if not self.initialize_components():
                logger.error("Nepodarilo sa inicializovat komponenty")
                return False
            
            # Cakanie na QR kod
            if not self.wait_for_qr_code():
                return False
            
            # Odpocitavanie
            self.countdown()
            
            # Hlavna jazda
            self.drive_sequence()
            
            logger.info("Kvalifikacny program uspesne ukonceny")
            return True
            
        except KeyboardInterrupt:
            logger.info("Program ukonceny cez Ctrl+C")
            return False
        except Exception as e:
            logger.error(f"Neocakavana chyba: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Ukoncenie a upratanie"""
        logger.info("Ukoncujem program...")
        self.running = False
        
        if self.robot:
            self.robot.stop_motors()
            time.sleep(0.2)  # Kratke cakanie
            self.robot.stop_motors()  # Druhe zastavenie pre istotu
            self.robot.stop()
            logger.info("Motordriver ukonceny")
        
        if self.camera:
            self.camera.cleanup()
            logger.info("Kamera ukoncena")
        
        cv2.destroyAllWindows()
        print("=== PROGRAM UKONCENY ===")

def main():
    """Hlavna funkcia"""
    print("Kvalifikacny program pre Robotour")
    print("Stlac Enter pre pokracovanie alebo Ctrl+C pre ukoncenie...")

    try:
        input()
    except KeyboardInterrupt:
        print("Program ukonceny")
        return

    # Moznost vybrania portov a nastaveni
    print("\nNastavenie:")
    motor_port = input("Motor port (Enter pre COM4): ").strip() or "COM4"
    camera_index = input("Camera index (Enter pre 1): ").strip() or "1"
    obstacle_threshold = input("Prah detekcie prekazky v % (Enter pre 20%): ").strip() or "20"

    try:
        camera_index = int(camera_index)
    except ValueError:
        camera_index = 1

    try:
        obstacle_threshold = float(obstacle_threshold)
    except ValueError:
        obstacle_threshold = 20.0

    print(f"\nNastavenia:")
    print(f"Motor port: {motor_port}")
    print(f"Camera index: {camera_index}")
    print(f"Prah detekcie prekazky: {obstacle_threshold}%")

    # Spustenie programu
    program = KvalifikacnyProgram(motor_port=motor_port, camera_index=camera_index, obstacle_threshold=obstacle_threshold)
    program.run()

if __name__ == "__main__":
    main()
