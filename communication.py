#!/usr/bin/env python3
"""
Communication Module for Robotic Vehicle
Wireless communication module for external connectivity and telemetry.
Handles remote monitoring, control commands, and data transmission.
"""

import threading
import time
import json
import socket
import select
from queue import Empty, Queue
from typing import Dict, List, Optional, Tuple


class CommunicationModule:
    """Communication module for wireless connectivity and telemetry."""
    
    def __init__(self):
        """Initialize the communication module."""
        self.module_name = "communication"
        
        # Communication configuration
        self.server_host = "0.0.0.0"
        self.server_port = 8080
        self.max_connections = 5
        self.connection_timeout = 30.0  # seconds
        
        # Server socket
        self.server_socket = None
        self.server_running = False
        self.client_connections = {}
        self.next_client_id = 1
        
        # Message queues
        self.outgoing_messages = Queue()
        self.incoming_messages = Queue()
        
        # Telemetry configuration
        self.telemetry_enabled = True
        self.telemetry_interval = 1.0  # seconds
        self.last_telemetry_time = 0
        self.telemetry_data = {}
        
        # Remote control
        self.remote_control_enabled = False
        self.authorized_clients = set()
        self.control_timeout = 5.0  # seconds for control commands
        
        # Message types
        self.message_types = {
            'telemetry': 'TELEMETRY',
            'status': 'STATUS',
            'command': 'COMMAND',
            'response': 'RESPONSE',
            'heartbeat': 'HEARTBEAT',
            'error': 'ERROR'
        }
        
        # Communication statistics
        self.comm_stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'bytes_sent': 0,
            'bytes_received': 0,
            'connections_total': 0,
            'connections_active': 0,
            'errors': 0,
            'start_time': time.time()
        }
        
        # Communication status
        self.comm_faults = []
        self.connection_errors = 0
        self.max_connection_errors = 10
        
        # Thread control
        self.running = False
        
    def initialize_server(self) -> bool:
        """Initialize the communication server."""
        try:
            # Create server socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.setblocking(False)
            
            # Bind and listen
            self.server_socket.bind((self.server_host, self.server_port))
            self.server_socket.listen(self.max_connections)
            
            self.server_running = True
            return True
            
        except Exception as e:
            self.add_fault(f"Error initializing server: {e}")
            return False
    
    def accept_connections(self):
        """Accept new client connections."""
        try:
            if not self.server_running or not self.server_socket:
                return
            
            # Use select to check for pending connections
            ready, _, _ = select.select([self.server_socket], [], [], 0)
            
            if ready:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    client_socket.setblocking(False)
                    
                    # Create client connection record
                    client_id = self.next_client_id
                    self.next_client_id += 1
                    
                    self.client_connections[client_id] = {
                        'socket': client_socket,
                        'address': client_address,
                        'connected_time': time.time(),
                        'last_activity': time.time(),
                        'authorized': False,
                        'message_count': 0
                    }
                    
                    # Update statistics
                    self.comm_stats['connections_total'] += 1
                    self.comm_stats['connections_active'] = len(self.client_connections)
                    
                    # Send welcome message
                    welcome_msg = {
                        'type': self.message_types['status'],
                        'message': 'Connected to robot communication server',
                        'client_id': client_id,
                        'timestamp': time.time()
                    }
                    self.send_message_to_client(client_id, welcome_msg)
                    
                except socket.error as e:
                    self.add_fault(f"Error accepting connection: {e}")
                    
        except Exception as e:
            self.add_fault(f"Error in accept_connections: {e}")
    
    def handle_client_messages(self):
        """Handle messages from connected clients."""
        try:
            clients_to_remove = []
            
            for client_id, client_info in self.client_connections.items():
                try:
                    client_socket = client_info['socket']
                    
                    # Check for incoming data
                    ready, _, _ = select.select([client_socket], [], [], 0)
                    
                    if ready:
                        try:
                            data = client_socket.recv(4096)
                            if not data:
                                # Client disconnected
                                clients_to_remove.append(client_id)
                                continue
                            
                            # Update activity time
                            client_info['last_activity'] = time.time()
                            client_info['message_count'] += 1
                            
                            # Update statistics
                            self.comm_stats['messages_received'] += 1
                            self.comm_stats['bytes_received'] += len(data)
                            
                            # Process received data
                            self.process_client_data(client_id, data)
                            
                        except socket.error as e:
                            if e.errno != socket.EWOULDBLOCK:
                                clients_to_remove.append(client_id)
                                self.add_fault(f"Client {client_id} socket error: {e}")
                    
                    # Check for client timeout
                    if (time.time() - client_info['last_activity']) > self.connection_timeout:
                        clients_to_remove.append(client_id)
                        self.add_fault(f"Client {client_id} timed out")
                        
                except Exception as e:
                    clients_to_remove.append(client_id)
                    self.add_fault(f"Error handling client {client_id}: {e}")
            
            # Remove disconnected clients
            for client_id in clients_to_remove:
                self.disconnect_client(client_id)
                
        except Exception as e:
            self.add_fault(f"Error handling client messages: {e}")
    
    def process_client_data(self, client_id: int, data: bytes):
        """Process data received from a client."""
        try:
            # Decode and parse JSON message
            message_str = data.decode('utf-8').strip()
            
            # Handle multiple messages in one packet
            for line in message_str.split('\n'):
                if line.strip():
                    try:
                        message = json.loads(line)
                        self.process_client_message(client_id, message)
                    except json.JSONDecodeError as e:
                        self.send_error_to_client(client_id, f"Invalid JSON: {e}")
                        
        except Exception as e:
            self.add_fault(f"Error processing client data: {e}")
            self.send_error_to_client(client_id, f"Data processing error: {e}")
    
    def process_client_message(self, client_id: int, message: Dict):
        """Process a parsed message from a client."""
        try:
            msg_type = message.get('type')
            
            if msg_type == self.message_types['command']:
                self.handle_remote_command(client_id, message)
            elif msg_type == self.message_types['heartbeat']:
                self.handle_client_heartbeat(client_id, message)
            elif msg_type == 'auth':
                self.handle_client_authentication(client_id, message)
            elif msg_type == 'subscribe':
                self.handle_subscription_request(client_id, message)
            else:
                self.send_error_to_client(client_id, f"Unknown message type: {msg_type}")
                
        except Exception as e:
            self.add_fault(f"Error processing message from client {client_id}: {e}")
            self.send_error_to_client(client_id, f"Message processing error: {e}")
    
    def handle_remote_command(self, client_id: int, message: Dict):
        """Handle remote control command from client."""
        try:
            if not self.remote_control_enabled:
                self.send_error_to_client(client_id, "Remote control is disabled")
                return
            
            if client_id not in self.authorized_clients:
                self.send_error_to_client(client_id, "Not authorized for remote control")
                return
            
            # Extract command details
            command = message.get('command')
            target_module = message.get('module')
            parameters = message.get('parameters', {})
            
            if not command or not target_module:
                self.send_error_to_client(client_id, "Missing command or module")
                return
            
            # Add command to incoming messages queue for main controller
            remote_command = {
                'module': target_module,
                'action': command,
                'parameters': parameters,
                'source': 'remote_client',
                'client_id': client_id,
                'timestamp': time.time()
            }
            
            self.incoming_messages.put(remote_command)
            
            # Send acknowledgment
            response = {
                'type': self.message_types['response'],
                'status': 'accepted',
                'command': command,
                'module': target_module,
                'timestamp': time.time()
            }
            self.send_message_to_client(client_id, response)
            
        except Exception as e:
            self.add_fault(f"Error handling remote command: {e}")
            self.send_error_to_client(client_id, f"Command handling error: {e}")
    
    def handle_client_heartbeat(self, client_id: int, message: Dict):
        """Handle heartbeat from client."""
        try:
            # Update client activity
            if client_id in self.client_connections:
                self.client_connections[client_id]['last_activity'] = time.time()
            
            # Send heartbeat response
            response = {
                'type': self.message_types['heartbeat'],
                'status': 'alive',
                'timestamp': time.time()
            }
            self.send_message_to_client(client_id, response)
            
        except Exception as e:
            self.add_fault(f"Error handling heartbeat: {e}")
    
    def handle_client_authentication(self, client_id: int, message: Dict):
        """Handle client authentication request."""
        try:
            # Simple authentication (in real implementation, use proper auth)
            auth_token = message.get('token')
            
            # For demo purposes, accept any token that starts with "robot_"
            if auth_token and auth_token.startswith('robot_'):
                self.authorized_clients.add(client_id)
                if client_id in self.client_connections:
                    self.client_connections[client_id]['authorized'] = True
                
                response = {
                    'type': self.message_types['response'],
                    'status': 'authenticated',
                    'message': 'Authentication successful',
                    'timestamp': time.time()
                }
            else:
                response = {
                    'type': self.message_types['error'],
                    'status': 'authentication_failed',
                    'message': 'Invalid authentication token',
                    'timestamp': time.time()
                }
            
            self.send_message_to_client(client_id, response)
            
        except Exception as e:
            self.add_fault(f"Error handling authentication: {e}")
    
    def handle_subscription_request(self, client_id: int, message: Dict):
        """Handle telemetry subscription request."""
        try:
            # For simplicity, all connected clients receive telemetry
            response = {
                'type': self.message_types['response'],
                'status': 'subscribed',
                'message': 'Subscribed to telemetry updates',
                'timestamp': time.time()
            }
            self.send_message_to_client(client_id, response)
            
        except Exception as e:
            self.add_fault(f"Error handling subscription: {e}")
    
    def send_message_to_client(self, client_id: int, message: Dict):
        """Send a message to a specific client."""
        try:
            if client_id not in self.client_connections:
                return
            
            client_socket = self.client_connections[client_id]['socket']
            message_str = json.dumps(message) + '\n'
            message_bytes = message_str.encode('utf-8')
            
            try:
                client_socket.send(message_bytes)
                
                # Update statistics
                self.comm_stats['messages_sent'] += 1
                self.comm_stats['bytes_sent'] += len(message_bytes)
                
            except socket.error as e:
                self.add_fault(f"Error sending to client {client_id}: {e}")
                self.disconnect_client(client_id)
                
        except Exception as e:
            self.add_fault(f"Error in send_message_to_client: {e}")
    
    def send_error_to_client(self, client_id: int, error_message: str):
        """Send an error message to a client."""
        error_msg = {
            'type': self.message_types['error'],
            'message': error_message,
            'timestamp': time.time()
        }
        self.send_message_to_client(client_id, error_msg)
    
    def broadcast_message(self, message: Dict):
        """Broadcast a message to all connected clients."""
        try:
            for client_id in list(self.client_connections.keys()):
                self.send_message_to_client(client_id, message)
                
        except Exception as e:
            self.add_fault(f"Error broadcasting message: {e}")
    
    def send_telemetry(self, telemetry_data: Dict):
        """Send telemetry data to all connected clients."""
        try:
            if not self.telemetry_enabled:
                return
            
            telemetry_msg = {
                'type': self.message_types['telemetry'],
                'data': telemetry_data,
                'timestamp': time.time()
            }
            
            self.broadcast_message(telemetry_msg)
            
        except Exception as e:
            self.add_fault(f"Error sending telemetry: {e}")
    
    def disconnect_client(self, client_id: int):
        """Disconnect a client."""
        try:
            if client_id in self.client_connections:
                client_info = self.client_connections[client_id]
                
                try:
                    client_info['socket'].close()
                except:
                    pass
                
                # Remove from authorized clients
                self.authorized_clients.discard(client_id)
                
                # Remove from connections
                del self.client_connections[client_id]
                
                # Update statistics
                self.comm_stats['connections_active'] = len(self.client_connections)
                
        except Exception as e:
            self.add_fault(f"Error disconnecting client {client_id}: {e}")
    
    def cleanup_server(self):
        """Clean up server resources."""
        try:
            # Disconnect all clients
            for client_id in list(self.client_connections.keys()):
                self.disconnect_client(client_id)
            
            # Close server socket
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            self.server_running = False
            
        except Exception as e:
            self.add_fault(f"Error cleaning up server: {e}")
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.comm_faults.append(fault)
        self.comm_stats['errors'] += 1
        
        # Keep only recent faults (last 50)
        if len(self.comm_faults) > 50:
            self.comm_faults = self.comm_faults[-50:]
    
    def get_status(self) -> Dict:
        """Get current communication status."""
        status = 'running' if self.server_running else 'stopped'
        if self.comm_faults:
            status = 'error'
        elif self.connection_errors > self.max_connection_errors:
            status = 'connection_error'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'server_running': self.server_running,
                'server_port': self.server_port,
                'connected_clients': len(self.client_connections),
                'authorized_clients': len(self.authorized_clients),
                'remote_control_enabled': self.remote_control_enabled,
                'telemetry_enabled': self.telemetry_enabled,
                'statistics': self.comm_stats.copy(),
                'faults': self.comm_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'enable_remote_control':
                self.remote_control_enabled = True
            elif action == 'disable_remote_control':
                self.remote_control_enabled = False
            elif action == 'enable_telemetry':
                self.telemetry_enabled = True
            elif action == 'disable_telemetry':
                self.telemetry_enabled = False
            elif action == 'disconnect_client':
                client_id = command.get('client_id')
                if client_id:
                    self.disconnect_client(client_id)
            elif action == 'reset_faults':
                self.comm_faults.clear()
            elif action == 'reset_stats':
                self.comm_stats = {
                    'messages_sent': 0,
                    'messages_received': 0,
                    'bytes_sent': 0,
                    'bytes_received': 0,
                    'connections_total': 0,
                    'connections_active': len(self.client_connections),
                    'errors': 0,
                    'start_time': time.time()
                }
            else:
                self.add_fault(f"Unknown communication command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'Communication module started',
            'timestamp': time.time()
        })
        
        # Initialize server
        if not self.initialize_server():
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': 'Failed to initialize communication server',
                'timestamp': time.time()
            })
        
        last_status_time = 0
        status_interval = 2.0  # Send status every 2 seconds
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Handle server operations
                if self.server_running:
                    self.accept_connections()
                    self.handle_client_messages()
                
                # Send telemetry if enabled
                if (self.telemetry_enabled and 
                    current_time - self.last_telemetry_time >= self.telemetry_interval):
                    # Collect telemetry data from status queue
                    # In a real implementation, this would aggregate system status
                    self.send_telemetry({'system_time': current_time})
                    self.last_telemetry_time = current_time
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.1)
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'Communication module error: {e}',
                'timestamp': time.time()
            })
        finally:
            self.cleanup_server()
            self.running = False
            
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'Communication module stopped',
                'timestamp': time.time()
            })
