#!/usr/bin/env python3
"""
GPS Module for Robotic Vehicle
Fetches real-time geolocation.
Provides location updates to main, and optionally to Vision and OSM modules.
"""

import threading
import time
import random
import math
from queue import Empty
from typing import Dict, Optional, Tuple


class GPSModule:
    """GPS module for real-time geolocation tracking."""
    
    def __init__(self):
        """Initialize the GPS module."""
        self.module_name = "gps"
        
        # GPS state
        self.current_location = {
            'latitude': 0.0,
            'longitude': 0.0,
            'altitude': 0.0,
            'accuracy': 0.0,  # meters
            'timestamp': 0.0,
            'fix_quality': 0,  # 0=invalid, 1=GPS, 2=DGPS
            'satellites': 0
        }
        
        # GPS configuration
        self.update_rate = 1.0  # Hz (GPS updates per second)
        self.min_accuracy = 5.0  # minimum acceptable accuracy in meters
        self.fix_timeout = 30.0  # seconds to wait for GPS fix
        
        # Simulation parameters (for testing without real GPS)
        self.simulation_mode = True
        self.sim_start_lat = 49.2827  # Vancouver coordinates for simulation
        self.sim_start_lon = -123.1207
        self.sim_drift_rate = 0.00001  # degrees per second (simulated movement)
        
        # GPS status
        self.gps_connected = False
        self.fix_acquired = False
        self.last_fix_time = 0
        self.gps_faults = []
        
        # Thread control
        self.running = False
        
    def initialize_gps(self) -> bool:
        """Initialize GPS hardware/connection."""
        try:
            if self.simulation_mode:
                # Simulate GPS initialization
                self.gps_connected = True
                self.current_location.update({
                    'latitude': self.sim_start_lat,
                    'longitude': self.sim_start_lon,
                    'altitude': 100.0,
                    'accuracy': 3.0,
                    'fix_quality': 1,
                    'satellites': 8
                })
                self.fix_acquired = True
                self.last_fix_time = time.time()
                return True
            else:
                # Real GPS initialization would go here
                # This would involve opening serial port, configuring GPS receiver, etc.
                # For now, we'll simulate a successful connection
                self.gps_connected = True
                return True
                
        except Exception as e:
            self.add_fault(f"GPS initialization failed: {e}")
            return False
    
    def read_gps_data(self) -> Optional[Dict]:
        """Read GPS data from hardware or simulation."""
        try:
            if not self.gps_connected:
                return None
            
            if self.simulation_mode:
                return self.simulate_gps_reading()
            else:
                # Real GPS reading would go here
                # This would involve parsing NMEA sentences or binary GPS data
                return self.parse_real_gps_data()
                
        except Exception as e:
            self.add_fault(f"Error reading GPS data: {e}")
            return None
    
    def simulate_gps_reading(self) -> Dict:
        """Simulate GPS reading for testing purposes."""
        current_time = time.time()
        
        # Simulate small random movement
        lat_drift = (random.random() - 0.5) * self.sim_drift_rate
        lon_drift = (random.random() - 0.5) * self.sim_drift_rate
        
        # Add some noise to simulate GPS accuracy
        accuracy_noise = random.uniform(1.0, 5.0)
        
        # Simulate occasional GPS signal loss
        if random.random() < 0.05:  # 5% chance of temporary signal loss
            return {
                'latitude': 0.0,
                'longitude': 0.0,
                'altitude': 0.0,
                'accuracy': 999.0,
                'timestamp': current_time,
                'fix_quality': 0,
                'satellites': 0
            }
        
        # Update simulated position
        self.current_location['latitude'] += lat_drift
        self.current_location['longitude'] += lon_drift
        self.current_location['accuracy'] = accuracy_noise
        self.current_location['timestamp'] = current_time
        self.current_location['satellites'] = random.randint(6, 12)
        
        return self.current_location.copy()
    
    def parse_real_gps_data(self) -> Optional[Dict]:
        """Parse real GPS data (placeholder for actual implementation)."""
        # This would contain actual GPS parsing logic
        # For example, parsing NMEA sentences like:
        # $GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47
        
        # Placeholder implementation
        return {
            'latitude': 0.0,
            'longitude': 0.0,
            'altitude': 0.0,
            'accuracy': 999.0,
            'timestamp': time.time(),
            'fix_quality': 0,
            'satellites': 0
        }
    
    def validate_gps_data(self, gps_data: Dict) -> bool:
        """Validate GPS data quality and accuracy."""
        try:
            # Check fix quality
            if gps_data['fix_quality'] == 0:
                return False
            
            # Check accuracy
            if gps_data['accuracy'] > self.min_accuracy:
                return False
            
            # Check for reasonable coordinates
            lat = gps_data['latitude']
            lon = gps_data['longitude']
            
            if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                return False
            
            # Check timestamp freshness
            if time.time() - gps_data['timestamp'] > 5.0:
                return False
            
            return True
            
        except Exception as e:
            self.add_fault(f"Error validating GPS data: {e}")
            return False
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two GPS coordinates using Haversine formula."""
        try:
            # Convert to radians
            lat1_rad = math.radians(lat1)
            lon1_rad = math.radians(lon1)
            lat2_rad = math.radians(lat2)
            lon2_rad = math.radians(lon2)
            
            # Haversine formula
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            
            a = (math.sin(dlat/2)**2 + 
                 math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
            c = 2 * math.asin(math.sqrt(a))
            
            # Earth radius in meters
            earth_radius = 6371000
            distance = earth_radius * c
            
            return distance
            
        except Exception as e:
            self.add_fault(f"Error calculating distance: {e}")
            return 0.0
    
    def calculate_bearing(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate bearing from point 1 to point 2."""
        try:
            lat1_rad = math.radians(lat1)
            lat2_rad = math.radians(lat2)
            dlon_rad = math.radians(lon2 - lon1)
            
            y = math.sin(dlon_rad) * math.cos(lat2_rad)
            x = (math.cos(lat1_rad) * math.sin(lat2_rad) - 
                 math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon_rad))
            
            bearing_rad = math.atan2(y, x)
            bearing_deg = math.degrees(bearing_rad)
            
            # Normalize to 0-360 degrees
            bearing_deg = (bearing_deg + 360) % 360
            
            return bearing_deg
            
        except Exception as e:
            self.add_fault(f"Error calculating bearing: {e}")
            return 0.0
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message
        }
        self.gps_faults.append(fault)
        
        # Keep only recent faults (last 50)
        if len(self.gps_faults) > 50:
            self.gps_faults = self.gps_faults[-50:]
    
    def get_status(self) -> Dict:
        """Get current GPS status."""
        status = 'running' if self.fix_acquired else 'no_fix'
        if self.gps_faults:
            status = 'error'
        elif not self.gps_connected:
            status = 'disconnected'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'location': self.current_location.copy() if self.fix_acquired else None,
                'accuracy': self.current_location['accuracy'],
                'fix_quality': self.current_location['fix_quality'],
                'satellites': self.current_location['satellites'],
                'connected': self.gps_connected,
                'fix_acquired': self.fix_acquired,
                'faults': self.gps_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'reset_faults':
                self.gps_faults.clear()
            elif action == 'reinitialize':
                self.initialize_gps()
            elif action == 'set_simulation_mode':
                self.simulation_mode = command.get('enabled', True)
            else:
                self.add_fault(f"Unknown GPS command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'GPS module started',
            'timestamp': time.time()
        })
        
        # Initialize GPS
        if not self.initialize_gps():
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': 'Failed to initialize GPS',
                'timestamp': time.time()
            })
        
        last_status_time = 0
        status_interval = 1.0  # Send status every second
        update_interval = 1.0 / self.update_rate
        last_update_time = 0
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Update GPS reading
                if current_time - last_update_time >= update_interval:
                    gps_data = self.read_gps_data()
                    if gps_data and self.validate_gps_data(gps_data):
                        self.current_location = gps_data
                        self.fix_acquired = True
                        self.last_fix_time = current_time
                    else:
                        # Check for fix timeout
                        if current_time - self.last_fix_time > self.fix_timeout:
                            self.fix_acquired = False
                            self.add_fault("GPS fix timeout")
                    
                    last_update_time = current_time
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.1)
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'GPS module error: {e}',
                'timestamp': time.time()
            })
        finally:
            self.running = False
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'GPS module stopped',
                'timestamp': time.time()
            })
