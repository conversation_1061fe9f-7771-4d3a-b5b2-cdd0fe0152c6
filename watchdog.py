#!/usr/bin/env python3
"""
Watchdog Module for Robotic Vehicle
Monitors active threads and system heartbeat.
Restarts unresponsive modules or triggers safe shutdown if unrecoverable.
"""

import threading
import time
import psutil
import os
from queue import Empty
from typing import Dict, List, Optional


class WatchdogModule:
    """Watchdog module for monitoring system health and thread status."""
    
    def __init__(self):
        """Initialize the watchdog module."""
        self.module_name = "watchdog"
        
        # Thread monitoring
        self.monitored_modules = [
            'motors', 'gps', 'vision', 'osm', 'logging', 'communication'
        ]
        self.thread_health = {}
        self.heartbeat_timeout = 10.0  # seconds
        self.last_heartbeats = {}
        
        # System monitoring
        self.system_health = {
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'disk_usage': 0.0,
            'temperature': 0.0,
            'uptime': 0.0
        }
        
        # Health thresholds
        self.cpu_threshold = 80.0  # percent
        self.memory_threshold = 85.0  # percent
        self.disk_threshold = 90.0  # percent
        self.temperature_threshold = 70.0  # celsius
        
        # Recovery actions
        self.restart_attempts = {}
        self.max_restart_attempts = 3
        self.restart_cooldown = 30.0  # seconds between restart attempts
        self.last_restart_times = {}
        
        # Watchdog status
        self.watchdog_faults = []
        self.critical_errors = []
        self.system_stable = True
        self.emergency_shutdown_triggered = False
        
        # Thread control
        self.running = False
        self.monitoring_interval = 2.0  # seconds
        
    def initialize_monitoring(self):
        """Initialize thread and system monitoring."""
        try:
            # Initialize heartbeat tracking for all modules
            current_time = time.time()
            for module in self.monitored_modules:
                self.last_heartbeats[module] = current_time
                self.thread_health[module] = {
                    'status': 'unknown',
                    'last_heartbeat': current_time,
                    'response_time': 0.0,
                    'restart_count': 0,
                    'healthy': True
                }
                self.restart_attempts[module] = 0
                self.last_restart_times[module] = 0
            
            return True
            
        except Exception as e:
            self.add_fault(f"Error initializing monitoring: {e}")
            return False
    
    def check_thread_health(self):
        """Check health of all monitored threads."""
        try:
            current_time = time.time()
            
            for module in self.monitored_modules:
                last_heartbeat = self.last_heartbeats.get(module, 0)
                time_since_heartbeat = current_time - last_heartbeat
                
                # Update thread health status
                if time_since_heartbeat > self.heartbeat_timeout:
                    self.thread_health[module]['healthy'] = False
                    self.thread_health[module]['status'] = 'unresponsive'
                    
                    # Check if we should attempt restart
                    if self.should_restart_module(module):
                        self.request_module_restart(module)
                    else:
                        # Too many restart attempts, mark as critical
                        self.add_critical_error(f"Module {module} unrecoverable after {self.max_restart_attempts} restart attempts")
                        
                else:
                    self.thread_health[module]['healthy'] = True
                    self.thread_health[module]['status'] = 'healthy'
                    self.thread_health[module]['response_time'] = time_since_heartbeat
                
                self.thread_health[module]['last_heartbeat'] = last_heartbeat
                
        except Exception as e:
            self.add_fault(f"Error checking thread health: {e}")
    
    def should_restart_module(self, module: str) -> bool:
        """Determine if a module should be restarted."""
        try:
            current_time = time.time()
            
            # Check restart attempt count
            if self.restart_attempts[module] >= self.max_restart_attempts:
                return False
            
            # Check restart cooldown
            last_restart = self.last_restart_times[module]
            if current_time - last_restart < self.restart_cooldown:
                return False
            
            # Don't restart critical modules if system is unstable
            critical_modules = ['motors', 'watchdog']
            if module in critical_modules and not self.system_stable:
                return False
            
            return True
            
        except Exception as e:
            self.add_fault(f"Error checking restart conditions for {module}: {e}")
            return False
    
    def request_module_restart(self, module: str):
        """Request restart of a specific module."""
        try:
            current_time = time.time()
            
            # Update restart tracking
            self.restart_attempts[module] += 1
            self.last_restart_times[module] = current_time
            self.thread_health[module]['restart_count'] += 1
            
            # Log restart request
            self.add_fault(f"Requesting restart of module {module} (attempt {self.restart_attempts[module]})")
            
            # In a real implementation, this would send a restart command
            # to the main controller or module manager
            # For now, we'll just log the request
            
        except Exception as e:
            self.add_fault(f"Error requesting restart for {module}: {e}")
    
    def check_system_health(self):
        """Monitor overall system health metrics."""
        try:
            # CPU usage
            self.system_health['cpu_usage'] = psutil.cpu_percent(interval=0.1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.system_health['memory_usage'] = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.system_health['disk_usage'] = (disk.used / disk.total) * 100
            
            # System uptime
            boot_time = psutil.boot_time()
            self.system_health['uptime'] = time.time() - boot_time
            
            # Temperature (if available)
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    # Get CPU temperature if available
                    cpu_temps = temps.get('cpu_thermal', temps.get('coretemp', []))
                    if cpu_temps:
                        self.system_health['temperature'] = cpu_temps[0].current
            except:
                # Temperature monitoring not available on all systems
                self.system_health['temperature'] = 0.0
            
            # Check thresholds
            self.check_system_thresholds()
            
        except Exception as e:
            self.add_fault(f"Error checking system health: {e}")
    
    def check_system_thresholds(self):
        """Check if system metrics exceed safe thresholds."""
        try:
            warnings = []
            
            # Check CPU usage
            if self.system_health['cpu_usage'] > self.cpu_threshold:
                warnings.append(f"High CPU usage: {self.system_health['cpu_usage']:.1f}%")
            
            # Check memory usage
            if self.system_health['memory_usage'] > self.memory_threshold:
                warnings.append(f"High memory usage: {self.system_health['memory_usage']:.1f}%")
            
            # Check disk usage
            if self.system_health['disk_usage'] > self.disk_threshold:
                warnings.append(f"High disk usage: {self.system_health['disk_usage']:.1f}%")
            
            # Check temperature
            if self.system_health['temperature'] > self.temperature_threshold:
                warnings.append(f"High temperature: {self.system_health['temperature']:.1f}°C")
            
            # Update system stability
            if warnings:
                self.system_stable = False
                for warning in warnings:
                    self.add_fault(f"System warning: {warning}")
            else:
                self.system_stable = True
                
        except Exception as e:
            self.add_fault(f"Error checking system thresholds: {e}")
    
    def process_heartbeat(self, module: str, heartbeat_data: Dict):
        """Process heartbeat from a monitored module."""
        try:
            current_time = time.time()
            
            if module in self.monitored_modules:
                self.last_heartbeats[module] = current_time
                
                # Update thread health data
                if module in self.thread_health:
                    self.thread_health[module].update({
                        'last_heartbeat': current_time,
                        'status': heartbeat_data.get('status', 'unknown'),
                        'healthy': True
                    })
                
                # Reset restart attempts on successful heartbeat
                if self.restart_attempts.get(module, 0) > 0:
                    self.restart_attempts[module] = 0
                    
        except Exception as e:
            self.add_fault(f"Error processing heartbeat from {module}: {e}")
    
    def check_emergency_conditions(self):
        """Check for conditions that require emergency shutdown."""
        try:
            emergency_conditions = []
            
            # Check for multiple critical module failures
            unhealthy_critical = 0
            critical_modules = ['motors', 'gps', 'vision']
            
            for module in critical_modules:
                if not self.thread_health.get(module, {}).get('healthy', True):
                    unhealthy_critical += 1
            
            if unhealthy_critical >= 2:
                emergency_conditions.append(f"{unhealthy_critical} critical modules unhealthy")
            
            # Check for system resource exhaustion
            if (self.system_health['memory_usage'] > 95.0 or 
                self.system_health['cpu_usage'] > 95.0):
                emergency_conditions.append("System resources exhausted")
            
            # Check for excessive temperature
            if self.system_health['temperature'] > 80.0:
                emergency_conditions.append("Critical temperature exceeded")
            
            # Trigger emergency shutdown if conditions met
            if emergency_conditions and not self.emergency_shutdown_triggered:
                self.trigger_emergency_shutdown(emergency_conditions)
                
        except Exception as e:
            self.add_fault(f"Error checking emergency conditions: {e}")
    
    def trigger_emergency_shutdown(self, reasons: List[str]):
        """Trigger emergency shutdown of the system."""
        try:
            self.emergency_shutdown_triggered = True
            
            # Log emergency shutdown
            reason_str = "; ".join(reasons)
            self.add_critical_error(f"Emergency shutdown triggered: {reason_str}")
            
            # In a real implementation, this would:
            # 1. Send emergency stop to motors
            # 2. Signal main controller to shutdown
            # 3. Save critical data
            # 4. Initiate graceful shutdown sequence
            
        except Exception as e:
            self.add_fault(f"Error triggering emergency shutdown: {e}")
    
    def add_fault(self, fault_message: str):
        """Add a fault to the fault list."""
        timestamp = time.time()
        fault = {
            'timestamp': timestamp,
            'message': fault_message,
            'level': 'warning'
        }
        self.watchdog_faults.append(fault)
        
        # Keep only recent faults (last 100)
        if len(self.watchdog_faults) > 100:
            self.watchdog_faults = self.watchdog_faults[-100:]
    
    def add_critical_error(self, error_message: str):
        """Add a critical error."""
        timestamp = time.time()
        error = {
            'timestamp': timestamp,
            'message': error_message,
            'level': 'critical'
        }
        self.critical_errors.append(error)
        self.watchdog_faults.append(error)
        
        # Keep only recent critical errors (last 20)
        if len(self.critical_errors) > 20:
            self.critical_errors = self.critical_errors[-20:]
    
    def get_status(self) -> Dict:
        """Get current watchdog status."""
        status = 'running'
        if self.critical_errors:
            status = 'critical'
        elif self.watchdog_faults:
            status = 'warning'
        elif not self.system_stable:
            status = 'unstable'
        
        return {
            'module': self.module_name,
            'data': {
                'status': status,
                'system_stable': self.system_stable,
                'emergency_shutdown': self.emergency_shutdown_triggered,
                'thread_health': self.thread_health.copy(),
                'system_health': self.system_health.copy(),
                'critical_errors': self.critical_errors.copy(),
                'faults': self.watchdog_faults.copy(),
                'timestamp': time.time()
            }
        }
    
    def process_command(self, command: Dict):
        """Process incoming commands."""
        try:
            action = command.get('action')
            
            if action == 'heartbeat':
                module = command.get('from_module')
                heartbeat_data = command.get('data', {})
                if module:
                    self.process_heartbeat(module, heartbeat_data)
            elif action == 'reset_faults':
                self.watchdog_faults.clear()
            elif action == 'reset_critical_errors':
                self.critical_errors.clear()
            elif action == 'reset_restart_counts':
                for module in self.restart_attempts:
                    self.restart_attempts[module] = 0
            else:
                self.add_fault(f"Unknown watchdog command: {action}")
                
        except Exception as e:
            self.add_fault(f"Error processing command: {e}")
    
    def run(self, command_queue, status_queue, log_queue, shutdown_event):
        """Main thread execution loop."""
        self.running = True
        
        # Log module startup
        log_queue.put({
            'module': self.module_name,
            'level': 'INFO',
            'message': 'Watchdog module started',
            'timestamp': time.time()
        })
        
        # Initialize monitoring
        if not self.initialize_monitoring():
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': 'Failed to initialize watchdog monitoring',
                'timestamp': time.time()
            })
        
        last_status_time = 0
        status_interval = 2.0  # Send status every 2 seconds
        last_monitoring_time = 0
        
        try:
            while self.running and not shutdown_event.is_set():
                current_time = time.time()
                
                # Process commands
                try:
                    while True:
                        command = command_queue.get_nowait()
                        if command.get('module') == self.module_name:
                            self.process_command(command)
                except Empty:
                    pass
                
                # Perform monitoring checks
                if current_time - last_monitoring_time >= self.monitoring_interval:
                    self.check_thread_health()
                    self.check_system_health()
                    self.check_emergency_conditions()
                    last_monitoring_time = current_time
                
                # Send status update
                if current_time - last_status_time >= status_interval:
                    status_queue.put(self.get_status())
                    last_status_time = current_time
                
                # Brief sleep
                time.sleep(0.5)
                
        except Exception as e:
            log_queue.put({
                'module': self.module_name,
                'level': 'ERROR',
                'message': f'Watchdog module error: {e}',
                'timestamp': time.time()
            })
        finally:
            self.running = False
            log_queue.put({
                'module': self.module_name,
                'level': 'INFO',
                'message': 'Watchdog module stopped',
                'timestamp': time.time()
            })
