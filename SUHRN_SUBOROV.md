# Suhrn Suborov - Kvalifikacny Program Robotour

## Hlavne Subory

### `kvalifikacia_demo.py`
**Hlavny program pre kvalifikaciu**
- Integruje motordriver a camera kniznice
- Implementuje celkovu logiku kvalifikacnej jazdy
- QR kod detekcia, beeper, od<PERSON><PERSON><PERSON><PERSON><PERSON>, jazda, detekcia prekazok
- Graficke zobrazenie stavu v realnom case
- Podrobne logovanie vsetkych operacii

### `test_setup.py`
**Diagnosticky a testovaci skript**
- Test dostupnych seriovych portov
- Test dostupnych kamier
- Test QR kod detekcie
- Test beeper funkcionality
- Test zavislosti
- Interaktivny test kamery s QR detekciou

### `install.bat`
**Instalacny skript pre Windows**
- Kontrola Python instalacie
- Automaticka instalacia zavislosti
- Spustenie test setup
- Jednoduche pouzitie pre uzivatelov

## Dokumentacia

### `KVALIFIKACIA_README.md`
**Hlavna uzivatelska dokumentacia**
- Popis kvalifikacie a funkcionality programu
- Instalacia a spustenie
- Konfiguracia a nastavenia
- Ovladanie a bezpecnost
- Riešenie problemov
- Kompletny navod pre uzivatelov

### `TECHNICKA_DOKUMENTACIA.md`
**Technicka dokumentacia pre vyvojarov**
- Architektura systemu a komponenty
- Implementacne detaily
- Algoritmy a stavovy automat
- Konfiguracne parametre
- Bezpecnost a error handling
- Mozne rozšírenia a modifikacie

### `QR_KODY_INFO.md`
**Informacie o QR kodoch**
- Priklady QR kodov pre testovanie
- Ako vytvorit vlastne QR kody
- Tipy pre pouzitie a umiestnenie
- Riešenie problemov s QR detekciou

## Konfiguracne Subory

### `kvalifikacia_requirements.txt`
**Python zavislosti**
```
opencv-python>=4.5.0    # Kamera a QR detekcia
numpy>=1.21.0           # Spracovanie obrazu
pyserial>=3.5           # Komunikacia s motormi
```

## Pouzivane Kniznice (externe)

### `../motordriver/`
**Motor control kniznica**
- `robot_driver.py` - Hlavna kniznica
- `robot_driver_demo.py` - Demo program
- `robot_driver_documentation.md` - Dokumentacia

### `../camera/camera/`
**Camera processing kniznica**
- `camera_module.py` - Hlavna kniznica
- `demo.py` - Demo program
- `README.md` - Dokumentacia

## Struktura Projektu

```
robotour/
├── kvalifikacia_demo.py           # Hlavny program
├── test_setup.py                  # Diagnostika
├── install.bat                    # Instalacia (Windows)
├── kvalifikacia_requirements.txt  # Zavislosti
├── KVALIFIKACIA_README.md         # Uzivatelska dokumentacia
├── TECHNICKA_DOKUMENTACIA.md     # Vyvojarska dokumentacia
├── QR_KODY_INFO.md               # QR kody info
├── SUHRN_SUBOROV.md              # Tento subor
└── kvalifikacia.log              # Log subor (vytvoreny pri spusteni)
```

## Spustenie Programu

### Rychle Spustenie
```bash
# 1. Instalacia
install.bat

# 2. Test setup
python test_setup.py

# 3. Spustenie programu
python kvalifikacia_demo.py
```

### Manualna Instalacia
```bash
# 1. Instalacia zavislosti
pip install -r kvalifikacia_requirements.txt

# 2. Test komponentov
python test_setup.py

# 3. Spustenie
python kvalifikacia_demo.py
```

## Logovanie

### `kvalifikacia.log`
**Automaticky vytvoreny log subor**
- Vsetky akcie a stavy robota
- Detekcie QR kodov a prekazok
- Chyby a varovania
- Casove znacky pre debugging

## Poziadavky na System

### Hardware
- USB kamera (odporucane index 1)
- Seriovy port pre motordriver (COM4)
- Windows OS (pre beeper)

### Software
- Python 3.8+
- OpenCV 4.5+
- NumPy 1.21+
- PySerial 3.5+

## Funkcionalita

### Sekvencia Programu
1. **Inicializacia** - Test komponentov
2. **QR Cakanie** - Detekcia QR kodu pre start
3. **Odpocitavanie** - 3s countdown s beeper
4. **Jazda** - 10m jazda rychlostou 10%
5. **Detekcia** - Monitorovanie prekazok
6. **Reakcia** - 10s cakanie alebo obchod
7. **Ukoncenie** - Bezpecne zastavenie

### Graficke Rozhranie
- Zobrazenie kamery v realnom case
- Informacie o stave (rychlost, vzdialenost, prekazky)
- Progress bar pre prejdenu vzdialenost
- Farebne oznacenie stavov

## Bezpecnost

### Automaticke Zastavenie
- Pri detekcii prekazky
- Pri chybach komunikacie
- Pri manualom ukonceni (Ctrl+C, 'q')

### Error Handling
- Graceful shutdown pri chybach
- Automaticke upratanie zdrojov
- Podrobne logovanie chyb

## Podpora

### Diagnostika
- `test_setup.py` pre test vsetkych komponentov
- Podrobne logy v `kvalifikacia.log`
- Graficke zobrazenie stavu

### Dokumentacia
- Uzivatelska: `KVALIFIKACIA_README.md`
- Technicka: `TECHNICKA_DOKUMENTACIA.md`
- QR kody: `QR_KODY_INFO.md`

---

**Poznamka**: Vsetky subory su napisane bez diakritiky pre kompatibilitu s kniznicami. Program je optimalizovany pre Windows, ale mozno ho upravit pre Linux/Mac.
